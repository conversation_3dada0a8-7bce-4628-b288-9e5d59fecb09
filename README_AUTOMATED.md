# 🤖 نظام إنشاء الفيديوهات التلقائي المتطور

نظام متكامل لإنشاء ونشر فيديوهات Reddit تلقائياً على YouTube Shorts باستخدام الذكاء الاصطناعي.

## ✨ المميزات الجديدة

### 🎯 **التشغيل التلقائي المستمر**
- جدولة ذكية كل 10 ساعات (8 صباحاً و 8 مساءً)
- إعادة تشغيل تلقائي عند الأخطاء
- مراقبة مستمرة لحالة النظام

### 🤖 **الذكاء الاصطناعي المتقدم**
- **Gemini 2.5 Pro** لإنشاء عناوين جذابة بطابع شبابي
- أوصاف تلقائية مع هاشتاغات متنوعة
- محتوى محسن لـ YouTube Shorts

### 📤 **النشر التلقائي**
- رفع مباشر على **YouTube Shorts**
- إعداد metadata تلقائي
- دعم Google Service Account

### 📱 **إشعارات Telegram**
- تقارير النشر الناجح
- تنبيهات الأخطاء الفورية
- مراقبة حالة النظام

### 🌐 **واجهة مراقبة ويب**
- لوحة تحكم شاملة
- إحصائيات مباشرة
- سجلات الأخطاء
- أدوات التحكم

## 🚀 التثبيت والإعداد

### 1. **تثبيت المتطلبات**
```bash
pip install -r requirements.txt
python -m playwright install
```

### 2. **إعداد Reddit API**
1. اذهب إلى: https://www.reddit.com/prefs/apps
2. أنشئ تطبيق جديد من نوع "script"
3. احصل على `client_id` و `client_secret`
4. حدث ملف `config.toml`:

```toml
[reddit.creds]
client_id = "YOUR_REDDIT_CLIENT_ID"
client_secret = "YOUR_REDDIT_CLIENT_SECRET"
username = "YOUR_REDDIT_USERNAME"
password = "YOUR_REDDIT_PASSWORD"
```

### 3. **إعداد Google Service Account**
1. اذهب إلى: https://console.cloud.google.com/
2. أنشئ مشروع جديد أو استخدم موجود
3. فعّل YouTube Data API v3
4. أنشئ Service Account
5. حمّل ملف JSON وضعه في المجلد الرئيسي باسم `service_account.json`

### 4. **إعداد Telegram Bot** (اختياري)
البوت مُعد مسبقاً، لكن يمكنك تغيير الإعدادات في:
- `automation/telegram_bot.py`

### 5. **إعداد Gemini AI**
المفتاح مُعد مسبقاً، لكن يمكنك تغييره في:
- `automation/gemini_content_generator.py`

## 🎮 التشغيل

### **التشغيل التلقائي الكامل**
```bash
python run_automated_system.py
```

### **التشغيل اليدوي (فيديو واحد)**
```bash
python main.py
```

### **واجهة المراقبة فقط**
```bash
python automation/dashboard.py
```

## 📊 واجهة المراقبة

بعد التشغيل، افتح المتصفح على: **http://localhost:5000**

### المميزات:
- **لوحة التحكم**: إحصائيات مباشرة ومعلومات النظام
- **السجلات**: عرض سجلات الأخطاء والأنشطة
- **الإعدادات**: تحكم في إعدادات النظام
- **أدوات التحكم**: تشغيل/إيقاف/إعادة تشغيل

## 🔧 الإعدادات المتقدمة

### **تخصيص الجدولة**
في ملف `scheduler.py`:
```python
# تغيير التوقيت
schedule.every().day.at("08:00").do(self.create_video)  # 8 صباحاً
schedule.every().day.at("20:00").do(self.create_video)  # 8 مساءً

# أو تغيير الفترة
schedule.every(6).hours.do(self.create_video)  # كل 6 ساعات
```

### **تخصيص محتوى Gemini**
في ملف `automation/gemini_content_generator.py`:
```python
# تغيير قوالب العناوين
self.title_templates = [
    "عنوان مخصص 1! 😱",
    "عنوان مخصص 2! 🤯",
    # أضف المزيد...
]

# تغيير الهاشتاغات
self.popular_hashtags = [
    "#مخصص1", "#مخصص2", "#مخصص3"
    # أضف المزيد...
]
```

### **تخصيص إعدادات YouTube**
في ملف `automation/youtube_uploader.py`:
```python
# تغيير إعدادات الخصوصية
'status': {
    'privacyStatus': 'public',  # أو 'private' أو 'unlisted'
    'selfDeclaredMadeForKids': False,
}
```

## 📁 هيكل المشروع

```
RedditVideoMakerBot/
├── 🤖 automation/              # وحدات الأتمتة الجديدة
│   ├── dashboard.py            # واجهة المراقبة
│   ├── error_handler.py        # معالجة الأخطاء
│   ├── gemini_content_generator.py  # مولد المحتوى
│   ├── telegram_bot.py         # بوت التيليجرام
│   └── youtube_uploader.py     # رافع اليوتيوب
├── 📊 templates/               # قوالب واجهة الويب
├── 📝 logs/                    # سجلات النظام
├── 🎬 results/                 # الفيديوهات المنتجة
├── ⚙️ config.toml             # إعدادات النظام
├── 🔑 service_account.json    # مفتاح Google (يجب إضافته)
├── 🚀 run_automated_system.py # ملف التشغيل الرئيسي
├── 📅 scheduler.py            # نظام الجدولة
└── 📋 requirements.txt        # المتطلبات المحدثة
```

## 🔍 استكشاف الأخطاء

### **مشكلة: "ملف service_account.json مفقود"**
- تأكد من وضع ملف Google Service Account في المجلد الرئيسي
- تأكد من تسمية الملف بـ `service_account.json` بالضبط

### **مشكلة: "فشل في رفع الفيديو على YouTube"**
- تأكد من صحة ملف Service Account
- تأكد من تفعيل YouTube Data API v3
- تحقق من أذونات Service Account

### **مشكلة: "خطأ في Gemini AI"**
- تحقق من صحة مفتاح API
- تأكد من وجود اتصال بالإنترنت
- تحقق من حدود الاستخدام

### **مشكلة: "لا تصل إشعارات Telegram"**
- أرسل رسالة للبوت أولاً لتفعيل المحادثة
- تحقق من صحة رمز البوت

## 📈 الإحصائيات والمراقبة

### **السجلات**
- `logs/system.log` - سجل النظام العام
- `logs/scheduler.log` - سجل نظام الجدولة
- `logs/main.log` - سجل إنشاء الفيديوهات
- `logs/errors.json` - سجل الأخطاء المفصل

### **الإحصائيات**
- عدد الفيديوهات المنشأة
- عدد الفيديوهات المنشورة
- معدل النجاح
- استخدام الموارد

## 🛡️ الأمان والاستقرار

### **معالجة الأخطاء**
- إعادة المحاولة التلقائية
- استراتيجيات التعافي
- تسجيل مفصل للأخطاء

### **مراقبة الموارد**
- مراقبة استخدام الذاكرة
- مراقبة استخدام القرص
- تنبيهات الموارد المرتفعة

### **النسخ الاحتياطي**
- حفظ تلقائي للإعدادات
- أرشفة السجلات
- حفظ إحصائيات النظام

## 🎯 النصائح والحيل

1. **للحصول على أفضل النتائج**: اتركه يعمل لعدة أيام لتحليل الأداء
2. **لتوفير الموارد**: قم بتشغيله على VPS بدلاً من الكمبيوتر الشخصي
3. **للمراقبة**: استخدم واجهة الويب للمتابعة المستمرة
4. **للتخصيص**: عدّل قوالب Gemini حسب جمهورك

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من السجلات في مجلد `logs/`
2. راجع واجهة المراقبة على `http://localhost:5000`
3. تأكد من صحة جميع ملفات الإعداد

---

**🎉 استمتع بإنشاء المحتوى التلقائي!**
