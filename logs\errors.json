[{"id": "ERR_1752806575_0", "timestamp": "2025-07-18T02:42:55.248420", "type": "TimeoutError", "message": "Locator.screenshot: Timeout 30000ms exceeded.\nCall log:\n  - waiting for locator(\"[data-test-id=\\\"post-content\\\"]\")\n", "traceback": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\automation\\error_handler.py\", line 320, in wrapper\n    return func(*args, **kwargs)\n  File \"C:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\main.py\", line 105, in main\n    get_screenshots_of_reddit_posts(reddit_object, number_of_comments)\n    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\video_creation\\screenshot_downloader.py\", line 203, in get_screenshots_of_reddit_posts\n    raise e\n  File \"C:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\video_creation\\screenshot_downloader.py\", line 185, in get_screenshots_of_reddit_posts\n    page.locator('[data-test-id=\"post-content\"]').screenshot(path=postcontentpath)\n    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\playwright\\sync_api\\_generated.py\", line 17322, in screenshot\n    self._sync(\n    ~~~~~~~~~~^\n        self._impl_obj.screenshot(\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^\n    ...<11 lines>...\n        )\n        ^\n    )\n    ^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ~~~~~~~~~~~^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\playwright\\_impl\\_locator.py\", line 544, in screenshot\n    return await self._with_element(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^\n    ...<3 lines>...\n    )\n    ^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\playwright\\_impl\\_locator.py\", line 112, in _with_element\n    handle = await self.element_handle(timeout=timeout)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\playwright\\_impl\\_locator.py\", line 315, in element_handle\n    handle = await self._frame.wait_for_selector(\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n        self._selector, strict=True, state=\"attached\", **params\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\playwright\\_impl\\_frame.py\", line 341, in wait_for_selector\n    await self._channel.send(\n        \"waitForSelector\", self._timeout, locals_to_params(locals())\n    )\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    ...<3 lines>...\n    )\n    ^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\nplaywright._impl._errors.TimeoutError: Locator.screenshot: Timeout 30000ms exceeded.\nCall log:\n  - waiting for locator(\"[data-test-id=\\\"post-content\\\"]\")\n\n", "context": {"function": "main_video_creation"}, "severity": "critical", "resolved": false}, {"id": "ERR_1752812692_1", "timestamp": "2025-07-18T04:24:52.763015", "type": "RuntimeError", "message": "no running event loop", "traceback": "Traceback (most recent call last):\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\main.py\", line 108, in main\n    length, number_of_comments = save_text_to_mp3(reddit_object)\n                                 ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\video_creation\\voices.py\", line 49, in save_text_to_mp3\n    return text_to_mp3.run()\n           ~~~~~~~~~~~~~~~^^\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\TTS\\engine_wrapper.py\", line 83, in run\n    self.call_tts(\"title\", process_text(self.reddit_object[\"thread_title\"]))\n    ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\TTS\\engine_wrapper.py\", line 167, in call_tts\n    self.tts_module.run(\n    ~~~~~~~~~~~~~~~~~~~^\n        text,\n        ^^^^^\n        filepath=filepath,\n        ^^^^^^^^^^^^^^^^^^\n        random_voice=settings.config[\"settings\"][\"tts\"][\"random_voice\"],\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\TTS\\elevenlabs.py\", line 21, in run\n    self._run_free_version(text, filepath)\n    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\TTS\\elevenlabs.py\", line 52, in _run_free_version\n    raise Exception(f\"ElevenLabs Free API Error: {response.status_code} - {response.text}\")\nException: ElevenLabs Free API Error: 401 - {\"detail\":{\"status\":\"needs_authorization\",\"message\":\"Neither authorization header nor xi-api-key received, please provide one.\"}}\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\automation\\error_handler.py\", line 320, in wrapper\n    return func(*args, **kwargs)\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\main.py\", line 113, in main\n    smart_monitor.log_error(\"tts_engine_failure\", str(e), {\"reddit_id\": reddit_object.get(\"thread_id\")})\n    ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\automation\\smart_monitor.py\", line 61, in log_error\n    self._analyze_and_respond(error_type, error_message, context)\n    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\automation\\smart_monitor.py\", line 76, in _analyze_and_respond\n    asyncio.create_task(self._attempt_recovery(error_type, error_message, context))\n    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Program Files\\Python313\\Lib\\asyncio\\tasks.py\", line 407, in create_task\n    loop = events.get_running_loop()\nRuntimeError: no running event loop\n", "context": {"function": "main_video_creation"}, "severity": "critical", "resolved": false}, {"id": "ERR_1752813392_2", "timestamp": "2025-07-18T04:36:32.620737", "type": "Exception", "message": "ElevenLabs Free API Error: 401 - {\"detail\":{\"status\":\"needs_authorization\",\"message\":\"Neither authorization header nor xi-api-key received, please provide one.\"}}", "traceback": "Traceback (most recent call last):\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\automation\\error_handler.py\", line 320, in wrapper\n    return func(*args, **kwargs)\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\main.py\", line 108, in main\n    length, number_of_comments = save_text_to_mp3(reddit_object)\n                                 ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\video_creation\\voices.py\", line 49, in save_text_to_mp3\n    return text_to_mp3.run()\n           ~~~~~~~~~~~~~~~^^\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\TTS\\engine_wrapper.py\", line 83, in run\n    self.call_tts(\"title\", process_text(self.reddit_object[\"thread_title\"]))\n    ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\TTS\\engine_wrapper.py\", line 167, in call_tts\n    self.tts_module.run(\n    ~~~~~~~~~~~~~~~~~~~^\n        text,\n        ^^^^^\n        filepath=filepath,\n        ^^^^^^^^^^^^^^^^^^\n        random_voice=settings.config[\"settings\"][\"tts\"][\"random_voice\"],\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\TTS\\elevenlabs.py\", line 21, in run\n    self._run_free_version(text, filepath)\n    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\TTS\\elevenlabs.py\", line 52, in _run_free_version\n    raise Exception(f\"ElevenLabs Free API Error: {response.status_code} - {response.text}\")\nException: ElevenLabs Free API Error: 401 - {\"detail\":{\"status\":\"needs_authorization\",\"message\":\"Neither authorization header nor xi-api-key received, please provide one.\"}}\n", "context": {"function": "main_video_creation"}, "severity": "critical", "resolved": false}, {"id": "ERR_1752813755_3", "timestamp": "2025-07-18T04:42:35.178355", "type": "TimeoutError", "message": "Locator.screenshot: Timeout 30000ms exceeded.\nCall log:\n  - waiting for locator(\"#t1_n3seewj\")\n", "traceback": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\automation\\error_handler.py\", line 320, in wrapper\n    return func(*args, **kwargs)\n  File \"C:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\main.py\", line 120, in main\n    get_screenshots_of_reddit_posts(reddit_object, number_of_comments)\n    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\video_creation\\screenshot_downloader.py\", line 298, in get_screenshots_of_reddit_posts\n    page.locator(f\"#t1_{comment['comment_id']}\").screenshot(\n    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^\n        path=f\"assets/temp/{reddit_id}/png/comment_{idx}.png\"\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\playwright\\sync_api\\_generated.py\", line 17322, in screenshot\n    self._sync(\n    ~~~~~~~~~~^\n        self._impl_obj.screenshot(\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^\n    ...<11 lines>...\n        )\n        ^\n    )\n    ^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\playwright\\_impl\\_sync_base.py\", line 115, in _sync\n    return task.result()\n           ~~~~~~~~~~~^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\playwright\\_impl\\_locator.py\", line 544, in screenshot\n    return await self._with_element(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^\n    ...<3 lines>...\n    )\n    ^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\playwright\\_impl\\_locator.py\", line 112, in _with_element\n    handle = await self.element_handle(timeout=timeout)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\playwright\\_impl\\_locator.py\", line 315, in element_handle\n    handle = await self._frame.wait_for_selector(\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n        self._selector, strict=True, state=\"attached\", **params\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    )\n    ^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\playwright\\_impl\\_frame.py\", line 341, in wait_for_selector\n    await self._channel.send(\n        \"waitForSelector\", self._timeout, locals_to_params(locals())\n    )\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\playwright\\_impl\\_connection.py\", line 69, in send\n    return await self._connection.wrap_api_call(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    ...<3 lines>...\n    )\n    ^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\playwright\\_impl\\_connection.py\", line 558, in wrap_api_call\n    raise rewrite_error(error, f\"{parsed_st['apiName']}: {error}\") from None\nplaywright._impl._errors.TimeoutError: Locator.screenshot: Timeout 30000ms exceeded.\nCall log:\n  - waiting for locator(\"#t1_n3seewj\")\n\n", "context": {"function": "main_video_creation"}, "severity": "critical", "resolved": false}, {"id": "ERR_1752816188_4", "timestamp": "2025-07-18T05:23:08.977520", "type": "DownloadError", "message": "\u001b[0;31mERROR:\u001b[0m \r[download] Got error: HTTPSConnectionPool(host='rr3---sn-4g5ednz7.googlevideo.com', port=443): Read timed out. (read timeout=20.0)", "traceback": "Traceback (most recent call last):\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\automation\\error_handler.py\", line 320, in wrapper\n    return func(*args, **kwargs)\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\main.py\", line 135, in main\n    download_background_audio(bg_config[\"audio\"])\n    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\video_creation\\background.py\", line 118, in download_background_audio\n    ydl.download([uri])\n    ~~~~~~~~~~~~^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yt_dlp\\YoutubeDL.py\", line 3644, in download\n    self.__download_wrapper(self.extract_info)(\n    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^\n        url, force_generic_extractor=self.params.get('force_generic_extractor', False))\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yt_dlp\\YoutubeDL.py\", line 3617, in wrapper\n    res = func(*args, **kwargs)\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yt_dlp\\YoutubeDL.py\", line 1651, in extract_info\n    return self.__extract_info(url, self.get_info_extractor(key), download, extra_info, process)\n           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yt_dlp\\YoutubeDL.py\", line 1662, in wrapper\n    return func(self, *args, **kwargs)\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yt_dlp\\YoutubeDL.py\", line 1818, in __extract_info\n    return self.process_ie_result(ie_result, download, extra_info)\n           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yt_dlp\\YoutubeDL.py\", line 1877, in process_ie_result\n    ie_result = self.process_video_result(ie_result, download=download)\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yt_dlp\\YoutubeDL.py\", line 3050, in process_video_result\n    self.process_info(new_info)\n    ~~~~~~~~~~~~~~~~~^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yt_dlp\\YoutubeDL.py\", line 185, in wrapper\n    return func(self, *args, **kwargs)\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yt_dlp\\YoutubeDL.py\", line 3518, in process_info\n    success, real_download = self.dl(temp_filename, info_dict)\n                             ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yt_dlp\\YoutubeDL.py\", line 3238, in dl\n    return fd.download(name, new_info, subtitle)\n           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yt_dlp\\downloader\\common.py\", line 468, in download\n    ret = self.real_download(filename, info_dict)\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yt_dlp\\downloader\\http.py\", line 365, in real_download\n    for retry in RetryManager(self.params.get('retries'), self.report_retry):\n                 ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yt_dlp\\utils\\_utils.py\", line 5252, in __iter__\n    self.error_callback(self.error, self.attempt, self.retries)\n    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yt_dlp\\downloader\\common.py\", line 414, in report_retry\n    RetryManager.report_retry(\n    ~~~~~~~~~~~~~~~~~~~~~~~~~^\n        err, count, retries, info=self.__to_screen,\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    ...<2 lines>...\n        sleep_func=self.params.get('retry_sleep_functions', {}).get(is_frag or 'http'),\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n        suffix=f'fragment{\"s\" if frag_index is None else f\" {frag_index}\"}' if is_frag else None)\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yt_dlp\\utils\\_utils.py\", line 5259, in report_retry\n    return error(f'{e}. Giving up after {count - 1} retries') if count > 1 else error(str(e))\n                                                                                ~~~~~^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yt_dlp\\downloader\\common.py\", line 417, in <lambda>\n    error=IDENTITY if not fatal else lambda e: self.report_error(f'\\r[download] Got error: {e}'),\n                                               ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yt_dlp\\YoutubeDL.py\", line 1120, in report_error\n    self.trouble(f'{self._format_err(\"ERROR:\", self.Styles.ERROR)} {message}', *args, **kwargs)\n    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\yt_dlp\\YoutubeDL.py\", line 1059, in trouble\n    raise DownloadError(message, exc_info)\nyt_dlp.utils.DownloadError: \u001b[0;31mERROR:\u001b[0m \r[download] Got error: HTTPSConnectionPool(host='rr3---sn-4g5ednz7.googlevideo.com', port=443): Read timed out. (read timeout=20.0)\n", "context": {"function": "main_video_creation"}, "severity": "critical", "resolved": false}, {"id": "ERR_1752861211_5", "timestamp": "2025-07-18T17:53:31.005234", "type": "AttributeError", "message": "'NoneType' object has no attribute 'max_chars'", "traceback": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\automation\\error_handler.py\", line 320, in wrapper\n    return func(*args, **kwargs)\n  File \"C:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\main.py\", line 108, in main\n    length, number_of_comments = save_text_to_mp3(reddit_object)\n                                 ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\video_creation\\voices.py\", line 49, in save_text_to_mp3\n    return text_to_mp3.run()\n           ~~~~~~~~~~~~~~~^^\n  File \"C:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\TTS\\engine_wrapper.py\", line 104, in run\n    len(comment[\"comment_body\"]) > self.tts_module.max_chars\n                                   ^^^^^^^^^^^^^^^^^^^^^^^^^\nAttributeError: 'NoneType' object has no attribute 'max_chars'\n", "context": {"function": "main_video_creation"}, "severity": "critical", "resolved": false}, {"id": "ERR_1752862938_6", "timestamp": "2025-07-18T18:22:18.268366", "type": "NameError", "message": "name 'logger' is not defined", "traceback": "Traceback (most recent call last):\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\automation\\error_handler.py\", line 320, in wrapper\n    return func(*args, **kwargs)\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\main.py\", line 108, in main\n    length, number_of_comments = save_text_to_mp3(reddit_object)\n                                 ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\video_creation\\voices.py\", line 49, in save_text_to_mp3\n    return text_to_mp3.run()\n           ~~~~~~~~~~~~~~~^^\n  File \"c:\\Users\\<USER>\\Desktop\\RedditVideoMakerBot\\TTS\\engine_wrapper.py\", line 109, in run\n    logger.info(f\"🎤 معالجة {len(comments_to_process)} تعليق من أصل {len(self.reddit_object['comments'])}\")\n    ^^^^^^\nNameError: name 'logger' is not defined\n", "context": {"function": "main_video_creation"}, "severity": "critical", "resolved": false}]