#!/usr/bin/env python3
"""
نظام إشعارات مشاكل الإعدادات عبر التيليجرام
"""

import logging
import asyncio
from typing import Dict, List
from automation.telegram_bot import send_notification, send_error

logger = logging.getLogger(__name__)

class ConfigNotificationManager:
    """مدير إشعارات مشاكل الإعدادات"""
    
    def __init__(self):
        self.notification_templates = {
            "tiktok_session_missing": {
                "title": "🎵 مطلوب TikTok Session ID",
                "message": (
                    "⚠️ **TikTok TTS يحتاج Session ID**\n\n"
                    "🔧 **الحل السريع:**\n"
                    "1. اضغط /start\n"
                    "2. اختر '🔑 إدارة APIs'\n"
                    "3. اضغط '🎵 TikTok'\n"
                    "4. أرسل Session ID\n\n"
                    "💡 **أو استخدم البديل المجاني:**\n"
                    "اضغط '🎤 إعداد ElevenLabs مجاني' للحصول على صوت عالي الجودة بدون تكلفة!"
                ),
                "auto_fix_available": True
            },
            
            "elevenlabs_api_missing": {
                "title": "🎤 مطلوب ElevenLabs API أو الوضع المجاني",
                "message": (
                    "⚠️ **ElevenLabs يحتاج إعداد**\n\n"
                    "🆓 **الحل المجاني (مُوصى به):**\n"
                    "1. اضغط /start\n"
                    "2. اختر '🎤 إعداد ElevenLabs مجاني'\n"
                    "3. اختر صوت من القائمة\n"
                    "4. ✅ جاهز للاستخدام!\n\n"
                    "💰 **أو أضف API Key:**\n"
                    "اضغط '🔑 إدارة APIs' → '🎤 ElevenLabs'"
                ),
                "auto_fix_available": True
            },
            
            "reddit_credentials_missing": {
                "title": "🔐 مطلوب بيانات Reddit",
                "message": (
                    "⚠️ **بيانات Reddit غير مكتملة**\n\n"
                    "📝 **المطلوب:**\n"
                    "• Client ID\n"
                    "• Client Secret\n"
                    "• Username\n"
                    "• Password\n\n"
                    "🔧 **كيفية الحصول عليها:**\n"
                    "1. اذهب إلى reddit.com/prefs/apps\n"
                    "2. أنشئ تطبيق جديد (script)\n"
                    "3. انسخ البيانات إلى config.toml\n\n"
                    "📖 راجع REDDIT_API_SETUP_COMPLETE.md للتفاصيل"
                ),
                "auto_fix_available": False
            },
            
            "telegram_token_missing": {
                "title": "📱 مطلوب رمز بوت التيليجرام",
                "message": (
                    "⚠️ **رمز بوت التيليجرام مفقود**\n\n"
                    "🤖 **كيفية إنشاء البوت:**\n"
                    "1. ابحث عن @BotFather في التيليجرام\n"
                    "2. أرسل /newbot\n"
                    "3. اتبع التعليمات\n"
                    "4. انسخ الرمز إلى config.toml\n\n"
                    "📍 **الموقع في الملف:**\n"
                    "[telegram]\n"
                    "bot_token = \"YOUR_TOKEN_HERE\""
                ),
                "auto_fix_available": False
            }
        }
    
    def send_config_issue_notification(self, issue_type: str, details: Dict = None):
        """إرسال إشعار مشكلة إعدادات"""
        try:
            if issue_type not in self.notification_templates:
                # إشعار عام للمشاكل غير المعروفة
                self._send_generic_config_error(issue_type, details)
                return

            template = self.notification_templates[issue_type]

            message = f"**{template['title']}**\n\n{template['message']}"

            if template.get('auto_fix_available'):
                message += "\n\n🔧 **سيتم محاولة الإصلاح التلقائي...**"

            send_notification(message)

        except Exception as e:
            logger.error(f"فشل في إرسال إشعار الإعدادات: {str(e)}")
    
    def _send_generic_config_error(self, issue_type: str, details: Dict):
        """إرسال إشعار عام لمشكلة غير معروفة"""
        message = (
            f"⚠️ **مشكلة في الإعدادات: {issue_type}**\n\n"
            "🔧 **الحلول العامة:**\n"
            "1. تحقق من ملف config.toml\n"
            "2. استخدم /start لإدارة الإعدادات\n"
            "3. راجع ملفات التوثيق\n\n"
        )

        if details:
            message += f"📋 **التفاصيل:** {details}\n\n"

        message += "📱 استخدم بوت التيليجرام لإصلاح المشاكل بسهولة!"

        send_error(message)
    
    def send_auto_fix_success(self, fix_type: str, details: str = ""):
        """إرسال إشعار نجاح الإصلاح التلقائي"""
        try:
            success_messages = {
                "switched_to_elevenlabs_free": (
                    "🎉 **تم الإصلاح التلقائي بنجاح!**\n\n"
                    "✅ تم التبديل إلى ElevenLabs المجاني\n"
                    "🎤 الصوت الحالي: Rachel (عالي الجودة)\n"
                    "💰 مجاني تماماً - بدون API key\n\n"
                    "🔄 **يمكنك تغيير الصوت:**\n"
                    "/start → 🎤 إعداد ElevenLabs مجاني\n\n"
                    "🚀 البوت جاهز للعمل الآن!"
                ),

                "enabled_elevenlabs_free": (
                    "🎉 **تم تفعيل ElevenLabs المجاني!**\n\n"
                    "✅ تم إعداد الوضع المجاني\n"
                    "🎤 صوت عالي الجودة بدون تكلفة\n\n"
                    "🔄 **لتغيير الصوت:**\n"
                    "/start → 🎤 إعداد ElevenLabs مجاني\n\n"
                    "🚀 البوت جاهز للعمل!"
                )
            }

            message = success_messages.get(fix_type, f"✅ تم الإصلاح التلقائي: {fix_type}")

            if details:
                message += f"\n\n📋 {details}"

            send_notification(message)

        except Exception as e:
            logger.error(f"فشل في إرسال إشعار النجاح: {str(e)}")
    
    def send_startup_issues_summary(self, issues: List[Dict]):
        """إرسال ملخص مشاكل البدء"""
        try:
            if not issues:
                return

            message = "🚨 **مشاكل في الإعدادات تحتاج انتباه:**\n\n"

            for i, issue in enumerate(issues, 1):
                message += f"{i}. {issue.get('problem', 'مشكلة غير محددة')}\n"
                if issue.get('auto_fix'):
                    message += "   🔧 سيتم الإصلاح تلقائياً\n"
                else:
                    message += "   ⚠️ يحتاج تدخل يدوي\n"
                message += "\n"

            message += (
                "📱 **استخدم بوت التيليجرام لحل هذه المشاكل:**\n"
                "/start → اختر الإعداد المناسب\n\n"
                "💡 **نصيحة:** ابدأ بـ 'إعداد ElevenLabs مجاني' للحصول على صوت عالي الجودة فوراً!"
            )

            send_error(message)

        except Exception as e:
            logger.error(f"فشل في إرسال ملخص المشاكل: {str(e)}")

# إنشاء مثيل عام
config_notifier = ConfigNotificationManager()

# دوال مساعدة للاستخدام السريع
def notify_tiktok_session_missing():
    """إشعار نقص TikTok Session ID"""
    config_notifier.send_config_issue_notification("tiktok_session_missing")

def notify_elevenlabs_api_missing():
    """إشعار نقص ElevenLabs API"""
    config_notifier.send_config_issue_notification("elevenlabs_api_missing")

def notify_reddit_credentials_missing():
    """إشعار نقص بيانات Reddit"""
    config_notifier.send_config_issue_notification("reddit_credentials_missing")

def notify_auto_fix_success(fix_type: str, details: str = ""):
    """إشعار نجاح الإصلاح التلقائي"""
    config_notifier.send_auto_fix_success(fix_type, details)
