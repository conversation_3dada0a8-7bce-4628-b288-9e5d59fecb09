#!/usr/bin/env python3
"""
فحص شامل لجميع APIs والمتطلبات
"""

import os
import json
import toml
from pathlib import Path
import requests

def check_reddit_api():
    """فحص Reddit API"""
    print("🔍 فحص Reddit API...")
    try:
        config = toml.load("config.toml")
        reddit_creds = config.get("reddit", {}).get("creds", {})
        
        required_fields = ["client_id", "client_secret", "username", "password"]
        missing = [f for f in required_fields if not reddit_creds.get(f) or "YOUR_REDDIT" in str(reddit_creds.get(f, ""))]
        
        if missing:
            print(f"❌ Reddit API - بيانات مفقودة: {', '.join(missing)}")
            return False
        else:
            print("✅ Reddit API - جميع البيانات موجودة")
            return True
            
    except Exception as e:
        print(f"❌ Reddit API - خطأ: {e}")
        return False

def check_youtube_api():
    """فحص YouTube API"""
    print("🔍 فحص YouTube API...")
    
    # فحص ملف service account
    if Path("service_account.json").exists():
        try:
            with open("service_account.json", 'r') as f:
                data = json.load(f)
                if data.get("type") == "service_account":
                    print("✅ YouTube API - ملف service_account.json صحيح")
                    return True
                else:
                    print("❌ YouTube API - ملف service_account.json غير صحيح")
                    return False
        except Exception as e:
            print(f"❌ YouTube API - خطأ في قراءة الملف: {e}")
            return False
    
    # فحص ملف client_secret (OAuth)
    elif Path("client_secret.json").exists():
        print("⚠️ YouTube API - يوجد client_secret.json (OAuth) لكن نحتاج service_account.json للنشر التلقائي")
        return False
    else:
        print("❌ YouTube API - لا يوجد ملف مصادقة")
        return False

def check_gemini_api():
    """فحص Gemini AI API"""
    print("🔍 فحص Gemini AI...")
    try:
        config = toml.load("config.toml")
        api_key = config.get("gemini", {}).get("api_key", "")
        
        if api_key and api_key != "YOUR_GEMINI_API_KEY":
            print("✅ Gemini AI - مفتاح API موجود")
            return True
        else:
            print("❌ Gemini AI - مفتاح API مفقود")
            return False
            
    except Exception as e:
        print(f"❌ Gemini AI - خطأ: {e}")
        return False

def check_telegram_api():
    """فحص Telegram Bot API"""
    print("🔍 فحص Telegram Bot...")
    try:
        config = toml.load("config.toml")
        bot_token = config.get("telegram", {}).get("bot_token", "")
        
        if bot_token and bot_token != "YOUR_BOT_TOKEN":
            # اختبار سريع للبوت
            response = requests.get(f"https://api.telegram.org/bot{bot_token}/getMe", timeout=5)
            if response.status_code == 200:
                data = response.json()
                if data.get("ok"):
                    print(f"✅ Telegram Bot - يعمل: @{data['result']['username']}")
                    return True
                else:
                    print("❌ Telegram Bot - رمز غير صحيح")
                    return False
            else:
                print("❌ Telegram Bot - فشل في الاتصال")
                return False
        else:
            print("❌ Telegram Bot - رمز مفقود")
            return False
            
    except Exception as e:
        print(f"❌ Telegram Bot - خطأ: {e}")
        return False

def check_dependencies():
    """فحص المكتبات المطلوبة"""
    print("🔍 فحص المكتبات...")
    
    required_packages = [
        "praw", "toml", "requests", "google-generativeai", 
        "google-api-python-client", "python-telegram-bot"
    ]
    
    missing = []
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
        except ImportError:
            missing.append(package)
    
    if missing:
        print(f"❌ مكتبات مفقودة: {', '.join(missing)}")
        print("شغل: pip install -r requirements.txt")
        return False
    else:
        print("✅ جميع المكتبات مثبتة")
        return True

def show_next_steps():
    """عرض الخطوات التالية"""
    print("\n" + "="*60)
    print("📋 الخطوات التالية:")
    print("="*60)
    
    if not Path("service_account.json").exists():
        print("\n🔴 مطلوب: إعداد YouTube Service Account")
        print("1. اذهب إلى: https://console.cloud.google.com/")
        print("2. أنشئ مشروع جديد أو استخدم موجود")
        print("3. فعّل YouTube Data API v3")
        print("4. اذهب إلى APIs & Services → Credentials")
        print("5. انقر Create Credentials → Service Account")
        print("6. أنشئ Service Account وحمّل ملف JSON")
        print("7. أعد تسمية الملف إلى: service_account.json")
        print("8. ضعه في المجلد الرئيسي")
        
        print("\n💡 ملاحظة مهمة:")
        print("- Service Account مختلف عن OAuth client")
        print("- Service Account يسمح بالنشر التلقائي")
        print("- يجب إضافة Service Account كمدير للقناة في YouTube Studio")
    
    print("\n🟢 جاهز للاختبار:")
    print("python run_automated_system.py")

def main():
    """الدالة الرئيسية"""
    print("🤖 فحص شامل لجميع APIs")
    print("="*60)
    
    results = {
        "Reddit API": check_reddit_api(),
        "YouTube API": check_youtube_api(), 
        "Gemini AI": check_gemini_api(),
        "Telegram Bot": check_telegram_api(),
        "Dependencies": check_dependencies()
    }
    
    print("\n" + "="*60)
    print("📊 ملخص النتائج:")
    print("="*60)
    
    all_ready = True
    for api, status in results.items():
        status_icon = "✅" if status else "❌"
        print(f"{status_icon} {api}")
        if not status:
            all_ready = False
    
    print(f"\n🎯 الحالة العامة: {'✅ جاهز للتشغيل' if all_ready else '❌ يحتاج إعداد إضافي'}")
    
    if not all_ready:
        show_next_steps()
    else:
        print("\n🎉 جميع APIs جاهزة! يمكنك تشغيل النظام:")
        print("python run_automated_system.py")

if __name__ == "__main__":
    main()
