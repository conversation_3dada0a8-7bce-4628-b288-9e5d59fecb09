# 🎬 دليل إعداد YouTube OAuth مع إشعارات Telegram

## 🎯 نظرة عامة

تم تحديث النظام ليستخدم **OAuth 2.0** بدلاً من Service Account، مع إضافة نظام إشعارات Telegram متقدم لمراقبة التوكن وتجديده تلقائياً.

---

## ✨ المميزات الجديدة

### 🔐 **نظام OAuth 2.0:**
- ✅ مصادقة آمنة مع Google
- ✅ تجديد تلقائي للتوكن
- ✅ إدارة ذكية لانتهاء الصلاحية

### 📱 **إشعارات Telegram:**
- ✅ تحذيرات انتهاء التوكن
- ✅ إشعارات نجاح/فشل التجديد
- ✅ تعليمات المصادقة الجديدة
- ✅ مراقبة مستمرة للنظام

### 🤖 **الأتمتة الكاملة:**
- ✅ تجديد تلقائي للتوكن
- ✅ إعادة المحاولة عند الفشل
- ✅ مراقبة دورية للحالة

---

## 📋 خطوات الإعداد

### **1. إعداد Google Cloud Console**

#### أ) إنشاء مشروع:
1. اذهب إلى: https://console.cloud.google.com/
2. انقر "Select a project" → "New Project"
3. أدخل اسم المشروع: `reddit-video-maker`
4. انقر "Create"

#### ب) تفعيل YouTube Data API v3:
1. في القائمة الجانبية: "APIs & Services" → "Library"
2. ابحث عن: "YouTube Data API v3"
3. انقر عليه ثم "Enable"

#### ج) إنشاء OAuth 2.0 Client:
1. اذهب إلى: "APIs & Services" → "Credentials"
2. انقر "Create Credentials" → "OAuth 2.0 Client ID"
3. اختر Application type: **"Desktop application"**
4. أدخل Name: `YouTube Uploader`
5. انقر "Create"

#### د) تحميل ملف التصريح:
1. انقر على Client ID الذي أنشأته
2. انقر "Download JSON"
3. أعد تسمية الملف إلى: `client_secret.json`
4. انقل الملف إلى المجلد الرئيسي للبرنامج

### **2. إعداد المصادقة الأولية**

```bash
# تشغيل سكريبت الإعداد
python setup_youtube_auth.py
```

**ما سيحدث:**
1. سيفتح رابط المصادقة في المتصفح
2. سجل دخولك بحساب Google المرتبط بقناة YouTube
3. اقبل الأذونات المطلوبة
4. انسخ الكود من الرابط النهائي
5. شغل الأمر مع الكود:

```bash
python setup_youtube_auth.py --code YOUR_CODE_HERE
```

### **3. اختبار النظام**

```bash
# اختبار شامل للنظام
python test_youtube_oauth.py
```

**النتيجة المتوقعة:**
```
✅ ملف client_secret.json
✅ ملف الإعدادات  
✅ مدير التوكن
✅ رافع YouTube
✅ بوت Telegram
📈 النتيجة النهائية: 5/5 اختبار نجح
🎉 جميع الاختبارات نجحت! النظام جاهز للعمل.
```

---

## 🔧 الأوامر المفيدة

### **إدارة التوكن:**
```bash
# فحص حالة التوكن
python monitor_youtube_token.py --check

# تجديد التوكن يدوياً
python monitor_youtube_token.py --refresh

# مراقبة مستمرة للتوكن
python monitor_youtube_token.py --monitor
```

### **إعادة تعيين المصادقة:**
```bash
# حذف التوكن الحالي وإعداد جديد
python setup_youtube_auth.py --reset
python setup_youtube_auth.py
```

### **التحقق من التوكن الموجود:**
```bash
python setup_youtube_auth.py --check
```

---

## 📱 إشعارات Telegram

### **أنواع الإشعارات:**

#### 🟡 **تحذير انتهاء التوكن:**
```
⚠️ تحذير: انتهاء صلاحية توكن YouTube قريباً
⏰ الوقت المتبقي: 6 ساعة
🔄 الإجراء: سيتم محاولة تجديد التوكن تلقائياً عند الحاجة.
```

#### 🟢 **نجاح التجديد:**
```
✅ تم تجديد توكن YouTube بنجاح
🔄 التجديد: تلقائي
⏳ صالح لمدة: ساعة واحدة تقريباً
```

#### 🔴 **فشل التجديد:**
```
❌ فشل في تجديد توكن YouTube
📋 الإجراء المطلوب:
1. شغل الأمر التالي: python setup_youtube_auth.py
2. اتبع التعليمات للحصول على مصادقة جديدة
```

#### 🔐 **مصادقة جديدة مطلوبة:**
```
🔐 مطلوب إعداد مصادقة YouTube جديدة
📋 خطوات الإعداد:
الطريقة الأولى - التلقائية: python setup_youtube_auth.py
🔗 رابط المصادقة المباشر: https://accounts.google.com/...
```

---

## 🔄 التشغيل التلقائي

### **إضافة للنظام التلقائي:**

في ملف `run_automated_system.py`، سيتم:
1. فحص التوكن قبل كل عملية نشر
2. محاولة تجديد التوكن تلقائياً عند الحاجة
3. إرسال إشعارات Telegram عند أي مشكلة
4. إيقاف النشر مؤقتاً إذا فشلت المصادقة

### **مراقبة مستمرة:**

```bash
# تشغيل مراقب التوكن في الخلفية
nohup python monitor_youtube_token.py --monitor &
```

---

## ⚠️ استكشاف الأخطاء

### **خطأ: "Invalid client_secret.json"**
```
السبب: ملف client_secret.json غير صحيح أو مفقود
الحل: تأكد من تحميل ملف JSON صحيح من Google Cloud Console
```

### **خطأ: "Token expired and refresh failed"**
```
السبب: انتهت صلاحية refresh token
الحل: إعداد مصادقة جديدة باستخدام setup_youtube_auth.py
```

### **خطأ: "403 Forbidden"**
```
السبب: الحساب المستخدم لا يملك صلاحيات على القناة
الحل: تأكد من استخدام نفس حساب Google المالك للقناة
```

### **خطأ: "API not enabled"**
```
السبب: YouTube Data API v3 غير مفعل
الحل: فعّل API من Google Cloud Console
```

---

## 📊 مراقبة النظام

### **ملفات السجلات:**
- `youtube_token_monitor.log` - سجل مراقبة التوكن
- `automation.log` - سجل النظام التلقائي

### **ملفات البيانات:**
- `youtube_token.pickle` - التوكن المحفوظ
- `telegram_chat_id.txt` - معرف محادثة Telegram

### **فحص دوري:**
```bash
# فحص يومي للنظام
python test_youtube_oauth.py
```

---

## 🚀 التشغيل النهائي

بعد إكمال الإعداد:

```bash
# تشغيل النظام التلقائي
python run_automated_system.py
```

**ستحصل على:**
- ✅ نشر تلقائي للفيديوهات على YouTube
- ✅ إشعارات Telegram لجميع العمليات
- ✅ تجديد تلقائي للتوكن
- ✅ مراقبة مستمرة للنظام

---

## 📞 الدعم

إذا واجهت مشاكل:
1. شغل اختبار التشخيص: `python test_youtube_oauth.py`
2. تحقق من سجلات النظام
3. تأكد من صحة ملف `client_secret.json`
4. تحقق من إشعارات Telegram

**🎯 الهدف: الحصول على ✅ لجميع الاختبارات**
