#!/usr/bin/env python3
"""
أداة لإصلاح مشاكل ملفات الخلفية
- حذف الملفات الجزئية (.part)
- تحديث قائمة الخلفيات لتتطابق مع الملفات الموجودة
- إضافة الملفات المحلية الموجودة إلى القائمة
"""

import json
import os
from pathlib import Path
from utils.console import print_step, print_substep

def clean_partial_files():
    """حذف الملفات الجزئية (.part) من مجلد الخلفيات"""
    print_step("🧹 تنظيف الملفات الجزئية...")
    
    video_dir = Path("assets/backgrounds/video/")
    audio_dir = Path("assets/backgrounds/audio/")
    
    cleaned_files = []
    
    # تنظيف ملفات الفيديو الجزئية
    for part_file in video_dir.glob("*.part"):
        print_substep(f"حذف ملف جزئي: {part_file.name}", style="yellow")
        part_file.unlink()
        cleaned_files.append(part_file.name)
    
    # تنظيف ملفات الصوت الجزئية
    for part_file in audio_dir.glob("*.part"):
        print_substep(f"حذف ملف صوتي جزئي: {part_file.name}", style="yellow")
        part_file.unlink()
        cleaned_files.append(part_file.name)
    
    if cleaned_files:
        print_substep(f"تم حذف {len(cleaned_files)} ملف جزئي", style="green")
    else:
        print_substep("لا توجد ملفات جزئية للحذف", style="green")
    
    return cleaned_files

def scan_existing_files():
    """فحص الملفات الموجودة في مجلدات الخلفية"""
    print_step("🔍 فحص الملفات الموجودة...")
    
    video_dir = Path("assets/backgrounds/video/")
    audio_dir = Path("assets/backgrounds/audio/")
    
    existing_files = {
        'video': [],
        'audio': []
    }
    
    # فحص ملفات الفيديو
    video_extensions = ['.mp4', '.avi', '.mov', '.mkv']
    for file_path in video_dir.glob('*'):
        if file_path.is_file() and file_path.suffix.lower() in video_extensions:
            size_mb = file_path.stat().st_size / (1024 * 1024)
            existing_files['video'].append({
                'name': file_path.name,
                'size_mb': round(size_mb, 1)
            })
            print_substep(f"فيديو: {file_path.name} ({size_mb:.1f} MB)", style="green")
    
    # فحص ملفات الصوت
    audio_extensions = ['.mp3', '.wav', '.aac', '.m4a']
    for file_path in audio_dir.glob('*'):
        if file_path.is_file() and file_path.suffix.lower() in audio_extensions:
            size_mb = file_path.stat().st_size / (1024 * 1024)
            existing_files['audio'].append({
                'name': file_path.name,
                'size_mb': round(size_mb, 1)
            })
            print_substep(f"صوت: {file_path.name} ({size_mb:.1f} MB)", style="green")
    
    return existing_files

def update_background_config(existing_files):
    """تحديث ملف إعدادات الخلفيات ليتطابق مع الملفات الموجودة"""
    print_step("⚙️ تحديث إعدادات الخلفيات...")
    
    # قراءة الإعدادات الحالية
    video_config_path = Path("utils/background_videos.json")
    audio_config_path = Path("utils/background_audios.json")
    
    # تحديث إعدادات الفيديو
    if video_config_path.exists():
        with open(video_config_path, 'r', encoding='utf-8') as f:
            video_config = json.load(f)
        
        # إضافة الملفات المحلية الموجودة
        for video_file in existing_files['video']:
            filename = video_file['name']
            
            # إنشاء مفتاح للملف
            key_name = filename.replace('.mp4', '').replace('.avi', '').replace('.mov', '').replace('.mkv', '')
            key_name = key_name.replace('-', '_').replace(' ', '_').lower()
            
            # إضافة إلى القائمة إذا لم يكن موجوداً
            if key_name not in video_config:
                video_config[key_name] = [
                    "local_file",
                    filename,
                    "Local File",
                    "center"
                ]
                print_substep(f"إضافة فيديو محلي: {key_name} -> {filename}", style="green")
        
        # حفظ التحديث
        with open(video_config_path, 'w', encoding='utf-8') as f:
            json.dump(video_config, f, indent=4, ensure_ascii=False)
    
    # تحديث إعدادات الصوت
    if audio_config_path.exists():
        with open(audio_config_path, 'r', encoding='utf-8') as f:
            audio_config = json.load(f)
        
        # إضافة الملفات المحلية الموجودة
        for audio_file in existing_files['audio']:
            filename = audio_file['name']
            
            # إنشاء مفتاح للملف
            key_name = filename.replace('.mp3', '').replace('.wav', '').replace('.aac', '').replace('.m4a', '')
            key_name = key_name.replace('-', '_').replace(' ', '_').lower()
            
            # إضافة إلى القائمة إذا لم يكن موجوداً
            if key_name not in audio_config:
                audio_config[key_name] = [
                    "local_file",
                    filename,
                    "Local File"
                ]
                print_substep(f"إضافة صوت محلي: {key_name} -> {filename}", style="green")
        
        # حفظ التحديث
        with open(audio_config_path, 'w', encoding='utf-8') as f:
            json.dump(audio_config, f, indent=4, ensure_ascii=False)

def fix_minecraft_background():
    """إصلاح مشكلة خلفية minecraft المحددة"""
    print_step("🔧 إصلاح مشكلة خلفية minecraft...")
    
    video_dir = Path("assets/backgrounds/video/")
    
    # البحث عن ملفات parkour موجودة
    parkour_files = []
    for file_path in video_dir.glob('*'):
        if file_path.is_file() and 'parkour' in file_path.name.lower():
            parkour_files.append(file_path.name)
    
    if parkour_files:
        print_substep(f"تم العثور على ملفات parkour: {parkour_files}", style="green")
        
        # تحديث إعدادات minecraft لتستخدم ملف موجود
        video_config_path = Path("utils/background_videos.json")
        if video_config_path.exists():
            with open(video_config_path, 'r', encoding='utf-8') as f:
                video_config = json.load(f)
            
            # استخدام أول ملف parkour موجود
            best_file = parkour_files[0]
            video_config["minecraft"] = [
                "local_file",
                best_file,
                "Local Parkour",
                "center"
            ]
            
            # حفظ التحديث
            with open(video_config_path, 'w', encoding='utf-8') as f:
                json.dump(video_config, f, indent=4, ensure_ascii=False)
            
            print_substep(f"تم تحديث minecraft لاستخدام: {best_file}", style="green")
    else:
        print_substep("لم يتم العثور على ملفات parkour محلية", style="yellow")

def main():
    """الدالة الرئيسية لإصلاح مشاكل الخلفيات"""
    print_step("🛠️ بدء إصلاح مشاكل ملفات الخلفية...")
    
    # إنشاء المجلدات إذا لم تكن موجودة
    Path("assets/backgrounds/video/").mkdir(parents=True, exist_ok=True)
    Path("assets/backgrounds/audio/").mkdir(parents=True, exist_ok=True)
    
    # 1. تنظيف الملفات الجزئية
    clean_partial_files()
    
    # 2. فحص الملفات الموجودة
    existing_files = scan_existing_files()
    
    # 3. تحديث إعدادات الخلفيات
    update_background_config(existing_files)
    
    # 4. إصلاح مشكلة minecraft المحددة
    fix_minecraft_background()
    
    print_step("✅ تم إصلاح مشاكل ملفات الخلفية بنجاح!")
    print_substep("يمكنك الآن تشغيل البوت بدون مشاكل تحميل", style="green")

if __name__ == "__main__":
    main()
