@echo off
chcp 65001 >nul
title نظام إنشاء الفيديوهات التلقائي

echo.
echo ================================================
echo 🤖 نظام إنشاء الفيديوهات التلقائي
echo ================================================
echo.

echo 🔍 فحص Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python 3.10+ من: https://python.org
    pause
    exit /b 1
)

echo ✅ Python موجود

echo.
echo 📦 تثبيت المتطلبات...
pip install -r requirements.txt

echo.
echo 🎭 تثبيت Playwright...
python -m playwright install

echo.
echo 🔍 فحص الملفات المطلوبة...

if not exist "config.toml" (
    echo ❌ ملف config.toml مفقود
    echo يرجى إعداد Reddit API في ملف config.toml
    pause
    exit /b 1
)

if not exist "service_account.json" (
    echo ❌ ملف service_account.json مفقود
    echo يرجى إضافة ملف Google Service Account
    pause
    exit /b 1
)

echo ✅ جميع الملفات موجودة

echo.
echo 🚀 بدء تشغيل النظام...
echo 💡 لإيقاف النظام، اضغط Ctrl+C
echo 🌐 واجهة المراقبة: http://localhost:5000
echo.

python run_automated_system.py

echo.
echo تم إيقاف النظام
pause
