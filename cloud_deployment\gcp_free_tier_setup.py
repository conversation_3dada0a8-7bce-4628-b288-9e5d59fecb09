#!/usr/bin/env python3
"""
إعداد النظام للعمل على Google Cloud Free Tier فقط
يستخدم الموارد المجانية الدائمة بدون استهلاك الـ 300 دولار
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, Any

logger = logging.getLogger(__name__)

class GCPFreeTierConfig:
    """إعدادات محسنة لـ Google Cloud Free Tier"""
    
    def __init__(self):
        self.free_tier_limits = {
            # Compute Engine (Always Free)
            'compute': {
                'instance_type': 'e2-micro',  # الوحيد المجاني
                'regions': ['us-west1', 'us-central1', 'us-east1'],  # المناطق المجانية
                'disk_size_gb': 30,  # 30 GB مجاني
                'monthly_hours': 744,  # ساعات الشهر كاملة مجانية
                'snapshots_gb': 5  # 5 GB لقطات مجانية
            },
            
            # Cloud Storage (Always Free)
            'storage': {
                'standard_storage_gb': 5,  # 5 GB مجاني
                'egress_gb': 100,  # 100 GB نقل صادر مجاني
                'operations': 5000,  # 5000 عملية مجانية
                'regions': ['us-west1', 'us-central1', 'us-east1']
            },
            
            # Cloud Functions (Always Free)
            'functions': {
                'invocations': 2000000,  # 2 مليون استدعاء
                'gb_seconds': 400000,  # 400,000 GB-ثانية
                'cpu_seconds': 200000,  # 200,000 CPU-ثانية
                'memory_mb': 256  # الحد الأدنى للذاكرة
            },
            
            # App Engine (Always Free)
            'app_engine': {
                'f1_hours_daily': 28,  # 28 ساعة F1 يومياً
                'b1_hours_daily': 9,   # 9 ساعات B1 يومياً
                'storage_gb': 1,       # 1 GB تخزين
                'egress_gb': 10        # 10 GB نقل صادر
            },
            
            # Cloud Build (Always Free)
            'build': {
                'minutes_daily': 120   # 120 دقيقة بناء يومياً
            }
        }
        
    def get_optimized_config(self) -> Dict[str, Any]:
        """الحصول على إعدادات محسنة للاستخدام المجاني"""
        return {
            'deployment_strategy': 'hybrid',  # مزيج من الخدمات المجانية
            'primary_compute': 'app_engine',  # App Engine كخدمة رئيسية
            'video_processing': 'cloud_functions',  # معالجة الفيديو
            'storage': 'cloud_storage',  # تخزين الملفات
            'scheduling': 'cloud_scheduler',  # جدولة المهام
            'monitoring': 'lightweight',  # مراقبة خفيفة
            
            # إعدادات تحسين الاستهلاك
            'optimization': {
                'max_concurrent_videos': 1,  # فيديو واحد في المرة
                'cleanup_after_upload': True,  # تنظيف فوري
                'compress_assets': True,  # ضغط الملفات
                'use_minimal_dependencies': True,  # أقل المكتبات
                'memory_limit_mb': 256,  # حد الذاكرة
                'timeout_seconds': 540,  # 9 دقائق (أقل من 10)
                'temp_storage_cleanup': True  # تنظيف التخزين المؤقت
            }
        }

def create_app_engine_config():
    """إنشاء ملف app.yaml لـ App Engine"""
    app_yaml_content = """
runtime: python39
service: default

# إعدادات Always Free Tier
instance_class: F1
automatic_scaling:
  min_instances: 0
  max_instances: 1
  target_cpu_utilization: 0.6

# متغيرات البيئة
env_variables:
  GOOGLE_CLOUD_PROJECT: your-project-id
  DEPLOYMENT_TYPE: free_tier
  MAX_MEMORY_MB: 256
  CLEANUP_ENABLED: true

# معالجات الطلبات
handlers:
- url: /static
  static_dir: static
  
- url: /.*
  script: auto
  secure: always

# إعدادات الأمان
includes:
- include: security.yaml
"""
    
    with open('cloud_deployment/app.yaml', 'w', encoding='utf-8') as f:
        f.write(app_yaml_content.strip())
    
    logger.info("✅ تم إنشاء ملف app.yaml")

def create_cloud_function_config():
    """إنشاء إعدادات Cloud Functions"""
    function_config = {
        'name': 'video-processor',
        'runtime': 'python39',
        'memory': '256MB',  # الحد الأدنى المجاني
        'timeout': '540s',  # 9 دقائق
        'max_instances': 1,  # مثيل واحد فقط
        'environment_variables': {
            'CLEANUP_ENABLED': 'true',
            'MEMORY_LIMIT': '256',
            'TEMP_CLEANUP': 'true'
        },
        'trigger': {
            'type': 'http',
            'security_level': 'secure_always'
        }
    }
    
    with open('cloud_deployment/function_config.json', 'w', encoding='utf-8') as f:
        json.dump(function_config, f, indent=2, ensure_ascii=False)
    
    logger.info("✅ تم إنشاء إعدادات Cloud Functions")

def create_cloud_scheduler_jobs():
    """إنشاء مهام Cloud Scheduler"""
    scheduler_jobs = [
        {
            'name': 'morning-video-job',
            'description': 'إنشاء فيديو صباحي',
            'schedule': '0 8 * * *',  # 8 صباحاً يومياً
            'time_zone': 'Asia/Riyadh',
            'http_target': {
                'uri': 'https://your-project.appspot.com/create-video',
                'http_method': 'POST',
                'headers': {
                    'Content-Type': 'application/json'
                },
                'body': json.dumps({'type': 'morning'}).encode()
            }
        },
        {
            'name': 'evening-video-job',
            'description': 'إنشاء فيديو مسائي',
            'schedule': '0 20 * * *',  # 8 مساءً يومياً
            'time_zone': 'Asia/Riyadh',
            'http_target': {
                'uri': 'https://your-project.appspot.com/create-video',
                'http_method': 'POST',
                'headers': {
                    'Content-Type': 'application/json'
                },
                'body': json.dumps({'type': 'evening'}).encode()
            }
        }
    ]
    
    with open('cloud_deployment/scheduler_jobs.json', 'w', encoding='utf-8') as f:
        json.dump(scheduler_jobs, f, indent=2, ensure_ascii=False)
    
    logger.info("✅ تم إنشاء مهام Cloud Scheduler")

def create_storage_config():
    """إنشاء إعدادات Cloud Storage"""
    storage_config = {
        'buckets': [
            {
                'name': 'reddit-video-assets',
                'location': 'us-central1',  # منطقة مجانية
                'storage_class': 'STANDARD',  # الفئة المجانية
                'lifecycle': {
                    'rules': [
                        {
                            'action': {'type': 'Delete'},
                            'condition': {'age': 1}  # حذف بعد يوم واحد
                        }
                    ]
                }
            },
            {
                'name': 'reddit-video-results',
                'location': 'us-central1',
                'storage_class': 'STANDARD',
                'lifecycle': {
                    'rules': [
                        {
                            'action': {'type': 'Delete'},
                            'condition': {'age': 7}  # حذف بعد أسبوع
                        }
                    ]
                }
            }
        ]
    }
    
    with open('cloud_deployment/storage_config.json', 'w', encoding='utf-8') as f:
        json.dump(storage_config, f, indent=2, ensure_ascii=False)
    
    logger.info("✅ تم إنشاء إعدادات Cloud Storage")

def create_deployment_script():
    """إنشاء سكريبت النشر"""
    deploy_script = """#!/bin/bash

# سكريبت نشر النظام على Google Cloud Free Tier
echo "🚀 بدء نشر النظام على Google Cloud Free Tier..."
echo "💰 هذا السكريبت يستخدم الموارد المجانية الدائمة فقط (Always Free Tier)"
echo "🚫 لن يتم استهلاك أي من الـ 300 دولار المجانية"

# التحقق من gcloud CLI
if ! command -v gcloud &> /dev/null; then
    echo "❌ gcloud CLI غير مثبت. يرجى تثبيته من: https://cloud.google.com/sdk"
    exit 1
fi

# تسجيل الدخول
echo "🔐 تسجيل الدخول إلى Google Cloud..."
gcloud auth login

# تعيين المشروع
echo "📋 تعيين المشروع..."
read -p "أدخل معرف المشروع (أو اتركه فارغاً لإنشاء مشروع جديد): " PROJECT_ID

if [ -z "$PROJECT_ID" ]; then
    echo "🆕 إنشاء مشروع جديد..."
    PROJECT_ID="reddit-video-maker-$(date +%s)"
    gcloud projects create $PROJECT_ID --name="Reddit Video Maker Free Tier"
    echo "✅ تم إنشاء المشروع: $PROJECT_ID"
fi

gcloud config set project $PROJECT_ID

# التحقق من ربط الفوترة
echo "💳 التحقق من ربط الفوترة..."
BILLING_ACCOUNT=$(gcloud beta billing projects describe $PROJECT_ID --format="value(billingAccountName)" 2>/dev/null)

if [ -z "$BILLING_ACCOUNT" ]; then
    echo "⚠️  المشروع غير مربوط بحساب فوترة"
    echo "📝 يرجى ربط المشروع بحساب فوترة من:"
    echo "   https://console.cloud.google.com/billing/linkedaccount?project=$PROJECT_ID"
    read -p "اضغط Enter بعد ربط الفوترة..."
fi

# تفعيل الخدمات المطلوبة (المجانية فقط)
echo "⚙️ تفعيل الخدمات المجانية..."
gcloud services enable appengine.googleapis.com
gcloud services enable cloudfunctions.googleapis.com
gcloud services enable cloudscheduler.googleapis.com
gcloud services enable storage.googleapis.com
gcloud services enable cloudbuild.googleapis.com
gcloud services enable youtube.googleapis.com

# إنشاء App Engine (مجاني)
echo "🏗️ إنشاء تطبيق App Engine..."
if ! gcloud app describe &>/dev/null; then
    gcloud app create --region=us-central
else
    echo "✅ App Engine موجود مسبقاً"
fi

# إنشاء Cloud Storage buckets
echo "💾 إنشاء buckets التخزين..."
gsutil mb -l us-central1 gs://$PROJECT_ID-video-assets 2>/dev/null || echo "✅ Bucket assets موجود"
gsutil mb -l us-central1 gs://$PROJECT_ID-video-results 2>/dev/null || echo "✅ Bucket results موجود"

# إنشاء ملف lifecycle للتنظيف التلقائي
cat > /tmp/lifecycle.json << EOF
{
  "rule": [
    {
      "action": {"type": "Delete"},
      "condition": {"age": 1}
    }
  ]
}
EOF

# تطبيق lifecycle policies
gsutil lifecycle set /tmp/lifecycle.json gs://$PROJECT_ID-video-assets
gsutil lifecycle set /tmp/lifecycle.json gs://$PROJECT_ID-video-results

# تحديث app.yaml بمعرف المشروع
sed -i "s/your-project-id/$PROJECT_ID/g" cloud_deployment/app.yaml

# نشر Cloud Functions
echo "⚡ نشر Cloud Functions..."
gcloud functions deploy video-processor \\
    --runtime python39 \\
    --trigger-http \\
    --memory 256MB \\
    --timeout 540s \\
    --max-instances 1 \\
    --source cloud_deployment/functions \\
    --entry-point process_video \\
    --allow-unauthenticated \\
    --region us-central1

# نشر App Engine
echo "🚀 نشر التطبيق..."
gcloud app deploy cloud_deployment/app.yaml --quiet

# إنشاء Cloud Scheduler jobs
echo "⏰ إنشاء مهام الجدولة..."

# حذف المهام الموجودة إن وجدت
gcloud scheduler jobs delete morning-video-job --quiet 2>/dev/null || true
gcloud scheduler jobs delete evening-video-job --quiet 2>/dev/null || true
gcloud scheduler jobs delete cleanup-job --quiet 2>/dev/null || true

# إنشاء مهام جديدة
gcloud scheduler jobs create http morning-video-job \\
    --schedule="0 8 * * *" \\
    --uri="https://$PROJECT_ID.appspot.com/create-video" \\
    --http-method=POST \\
    --headers="Content-Type=application/json" \\
    --message-body='{"type":"morning"}' \\
    --time-zone="Asia/Riyadh"

gcloud scheduler jobs create http evening-video-job \\
    --schedule="0 20 * * *" \\
    --uri="https://$PROJECT_ID.appspot.com/create-video" \\
    --http-method=POST \\
    --headers="Content-Type=application/json" \\
    --message-body='{"type":"evening"}' \\
    --time-zone="Asia/Riyadh"

gcloud scheduler jobs create http cleanup-job \\
    --schedule="0 2 * * *" \\
    --uri="https://$PROJECT_ID.appspot.com/cleanup" \\
    --http-method=POST \\
    --time-zone="Asia/Riyadh"

echo ""
echo "🎉 تم النشر بنجاح!"
echo "=================================="
echo "🌐 رابط التطبيق: https://$PROJECT_ID.appspot.com"
echo "📊 لوحة التحكم: https://console.cloud.google.com/appengine?project=$PROJECT_ID"
echo "⚡ Cloud Functions: https://console.cloud.google.com/functions?project=$PROJECT_ID"
echo "📅 Cloud Scheduler: https://console.cloud.google.com/cloudscheduler?project=$PROJECT_ID"
echo "💾 Cloud Storage: https://console.cloud.google.com/storage?project=$PROJECT_ID"

echo ""
echo "📋 ملاحظات مهمة:"
echo "=================================="
echo "✅ جميع الخدمات المستخدمة مجانية ضمن Always Free Tier"
echo "✅ لن يتم استهلاك الـ 300 دولار المجانية"
echo "✅ الفيديوهات ستُحذف تلقائياً بعد يوم واحد لتوفير المساحة"
echo "✅ تنظيف تلقائي يومياً في الساعة 2 صباحاً"
echo "✅ إنشاء فيديوهات في الساعة 8 صباحاً و 8 مساءً"

echo ""
echo "📈 الاستخدام المتوقع:"
echo "=================================="
echo "• App Engine: ~20 ساعة يومياً (من أصل 28 مجانية)"
echo "• Cloud Functions: ~60 استدعاء شهرياً (من أصل 2M مجانية)"
echo "• Cloud Storage: ~2 GB (من أصل 5 GB مجانية)"
echo "• التكلفة الشهرية: 0.00$ - مجاني تماماً!"

echo ""
echo "🔧 الخطوات التالية:"
echo "=================================="
echo "1. تحديث بيانات Reddit API في app.yaml"
echo "2. إضافة ملف service_account.json لـ YouTube"
echo "3. اختبار النظام من لوحة التحكم"
echo "4. مراقبة الاستخدام لضمان البقاء ضمن الحدود المجانية"

echo ""
echo "🆘 في حالة المشاكل:"
echo "=================================="
echo "• تحقق من السجلات: gcloud app logs tail -s default"
echo "• راجع Cloud Functions: gcloud functions logs read video-processor"
echo "• تحقق من الاستخدام في لوحة التحكم"
"""
    
    with open('cloud_deployment/deploy.sh', 'w', encoding='utf-8') as f:
        f.write(deploy_script.strip())
    
    # جعل الملف قابل للتنفيذ
    os.chmod('cloud_deployment/deploy.sh', 0o755)
    
    logger.info("✅ تم إنشاء سكريبت النشر")

def create_monitoring_config():
    """إنشاء إعدادات مراقبة خفيفة"""
    monitoring_config = {
        'alerts': {
            'storage_usage': {
                'threshold_gb': 4,  # تنبيه عند 4 GB (من أصل 5)
                'action': 'cleanup_old_files'
            },
            'function_invocations': {
                'threshold': 1800000,  # تنبيه عند 1.8 مليون (من أصل 2)
                'action': 'reduce_frequency'
            },
            'app_engine_hours': {
                'threshold_daily': 25,  # تنبيه عند 25 ساعة (من أصل 28)
                'action': 'optimize_instances'
            }
        },
        'cleanup_policies': {
            'temp_files_hours': 1,  # حذف الملفات المؤقتة بعد ساعة
            'logs_days': 7,  # حذف السجلات بعد أسبوع
            'assets_days': 1,  # حذف الأصول بعد يوم
            'results_days': 7  # حذف النتائج بعد أسبوع
        }
    }
    
    with open('cloud_deployment/monitoring_config.json', 'w', encoding='utf-8') as f:
        json.dump(monitoring_config, f, indent=2, ensure_ascii=False)
    
    logger.info("✅ تم إنشاء إعدادات المراقبة")

def main():
    """إنشاء جميع ملفات الإعداد"""
    # إنشاء مجلد النشر
    Path('cloud_deployment').mkdir(exist_ok=True)
    Path('cloud_deployment/functions').mkdir(exist_ok=True)
    
    logger.info("🔧 إنشاء إعدادات Google Cloud Free Tier...")
    
    # إنشاء جميع ملفات الإعداد
    create_app_engine_config()
    create_cloud_function_config()
    create_cloud_scheduler_jobs()
    create_storage_config()
    create_deployment_script()
    create_monitoring_config()
    
    logger.info("✅ تم إنشاء جميع ملفات الإعداد بنجاح!")
    logger.info("📁 الملفات موجودة في مجلد: cloud_deployment/")
    logger.info("🚀 لبدء النشر، شغل: ./cloud_deployment/deploy.sh")

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    main()
