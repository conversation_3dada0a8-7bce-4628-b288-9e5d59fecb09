#!/usr/bin/env python3
"""
نظام إدارة الأخطاء والاستقرار
يوفر معالجة شاملة للأخطاء وإعادة المحاولة والتعافي التلقائي
"""

import logging
import traceback
import time
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from functools import wraps
import threading
from pathlib import Path

# إعداد السجلات
logger = logging.getLogger(__name__)

class ErrorHandler:
    def __init__(self):
        self.error_log_file = "logs/errors.json"
        self.max_retries = 3
        self.retry_delays = [5, 15, 30]  # ثوان
        self.error_history = []
        self.recovery_strategies = {}
        
        # إنشاء مجلد السجلات
        Path("logs").mkdir(exist_ok=True)
        
        # تحميل تاريخ الأخطاء
        self.load_error_history()
        
    def load_error_history(self):
        """تحميل تاريخ الأخطاء من الملف"""
        try:
            if os.path.exists(self.error_log_file):
                with open(self.error_log_file, 'r', encoding='utf-8') as f:
                    self.error_history = json.load(f)
        except Exception as e:
            logger.error(f"فشل في تحميل تاريخ الأخطاء: {e}")
            self.error_history = []
            
    def save_error_history(self):
        """حفظ تاريخ الأخطاء في الملف"""
        try:
            # الاحتفاظ بآخر 1000 خطأ فقط
            if len(self.error_history) > 1000:
                self.error_history = self.error_history[-1000:]
                
            with open(self.error_log_file, 'w', encoding='utf-8') as f:
                json.dump(self.error_history, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"فشل في حفظ تاريخ الأخطاء: {e}")
            
    def log_error(self, error: Exception, context: Dict[str, Any] = None, 
                  severity: str = "error") -> str:
        """
        تسجيل خطأ مع السياق
        
        Args:
            error: الخطأ المحدث
            context: معلومات إضافية عن السياق
            severity: مستوى الخطورة (error, warning, critical)
            
        Returns:
            معرف الخطأ
        """
        error_id = f"ERR_{int(time.time())}_{len(self.error_history)}"
        
        error_info = {
            'id': error_id,
            'timestamp': datetime.now().isoformat(),
            'type': type(error).__name__,
            'message': str(error),
            'traceback': traceback.format_exc(),
            'context': context or {},
            'severity': severity,
            'resolved': False
        }
        
        self.error_history.append(error_info)
        self.save_error_history()
        
        # تسجيل في السجل العادي
        logger.error(f"[{error_id}] {severity.upper()}: {error}")
        if context:
            logger.error(f"[{error_id}] السياق: {context}")
            
        # إرسال إشعار Telegram للأخطاء الحرجة
        if severity == "critical":
            try:
                from automation.telegram_bot import send_error
                send_error(f"خطأ حرج: {error}", traceback.format_exc())
            except:
                pass
                
        return error_id
        
    def retry_with_backoff(self, func: Callable, *args, max_retries: int = None, 
                          context: Dict[str, Any] = None, **kwargs) -> Any:
        """
        تنفيذ دالة مع إعادة المحاولة والانتظار المتزايد
        
        Args:
            func: الدالة المراد تنفيذها
            max_retries: عدد المحاولات القصوى
            context: معلومات السياق
            
        Returns:
            نتيجة الدالة أو None في حالة الفشل
        """
        if max_retries is None:
            max_retries = self.max_retries
            
        last_error = None
        
        for attempt in range(max_retries + 1):
            try:
                result = func(*args, **kwargs)
                
                # إذا نجحت بعد فشل، سجل التعافي
                if attempt > 0:
                    logger.info(f"تم التعافي بنجاح بعد {attempt} محاولة: {func.__name__}")
                    
                return result
                
            except Exception as e:
                last_error = e
                
                if attempt < max_retries:
                    delay = self.retry_delays[min(attempt, len(self.retry_delays) - 1)]
                    logger.warning(f"المحاولة {attempt + 1}/{max_retries + 1} فشلت: {e}")
                    logger.info(f"إعادة المحاولة خلال {delay} ثانية...")
                    time.sleep(delay)
                else:
                    # فشل نهائي
                    error_id = self.log_error(e, {
                        'function': func.__name__,
                        'attempts': max_retries + 1,
                        'context': context
                    }, "error")
                    logger.error(f"فشل نهائي بعد {max_retries + 1} محاولة: {e}")
                    
        return None
        
    def safe_execute(self, func: Callable, *args, fallback_value: Any = None,
                    context: Dict[str, Any] = None, **kwargs) -> Any:
        """
        تنفيذ آمن للدالة مع قيمة احتياطية
        
        Args:
            func: الدالة المراد تنفيذها
            fallback_value: القيمة الاحتياطية في حالة الفشل
            context: معلومات السياق
            
        Returns:
            نتيجة الدالة أو القيمة الاحتياطية
        """
        try:
            return func(*args, **kwargs)
        except Exception as e:
            self.log_error(e, {
                'function': func.__name__,
                'context': context
            }, "warning")
            return fallback_value
            
    def register_recovery_strategy(self, error_type: str, strategy: Callable):
        """
        تسجيل استراتيجية تعافي لنوع خطأ معين
        
        Args:
            error_type: نوع الخطأ
            strategy: دالة التعافي
        """
        self.recovery_strategies[error_type] = strategy
        logger.info(f"تم تسجيل استراتيجية تعافي لـ: {error_type}")
        
    def attempt_recovery(self, error: Exception, context: Dict[str, Any] = None) -> bool:
        """
        محاولة التعافي من خطأ باستخدام الاستراتيجيات المسجلة
        
        Args:
            error: الخطأ المحدث
            context: معلومات السياق
            
        Returns:
            True إذا تم التعافي بنجاح
        """
        error_type = type(error).__name__
        
        if error_type in self.recovery_strategies:
            try:
                logger.info(f"محاولة التعافي من {error_type}...")
                strategy = self.recovery_strategies[error_type]
                result = strategy(error, context)
                
                if result:
                    logger.info(f"تم التعافي بنجاح من {error_type}")
                    return True
                else:
                    logger.warning(f"فشل في التعافي من {error_type}")
                    
            except Exception as recovery_error:
                logger.error(f"خطأ في استراتيجية التعافي: {recovery_error}")
                
        return False
        
    def get_error_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الأخطاء"""
        if not self.error_history:
            return {
                'total_errors': 0,
                'recent_errors': 0,
                'error_types': {},
                'severity_distribution': {},
                'resolved_count': 0
            }
            
        # آخر 24 ساعة
        recent_threshold = datetime.now() - timedelta(hours=24)
        recent_errors = [
            err for err in self.error_history
            if datetime.fromisoformat(err['timestamp']) > recent_threshold
        ]
        
        # توزيع أنواع الأخطاء
        error_types = {}
        severity_distribution = {}
        resolved_count = 0
        
        for error in self.error_history:
            # نوع الخطأ
            error_type = error['type']
            error_types[error_type] = error_types.get(error_type, 0) + 1
            
            # مستوى الخطورة
            severity = error['severity']
            severity_distribution[severity] = severity_distribution.get(severity, 0) + 1
            
            # الأخطاء المحلولة
            if error.get('resolved', False):
                resolved_count += 1
                
        return {
            'total_errors': len(self.error_history),
            'recent_errors': len(recent_errors),
            'error_types': error_types,
            'severity_distribution': severity_distribution,
            'resolved_count': resolved_count,
            'resolution_rate': (resolved_count / len(self.error_history)) * 100 if self.error_history else 0
        }
        
    def mark_error_resolved(self, error_id: str):
        """تمييز خطأ كمحلول"""
        for error in self.error_history:
            if error['id'] == error_id:
                error['resolved'] = True
                error['resolved_at'] = datetime.now().isoformat()
                self.save_error_history()
                logger.info(f"تم تمييز الخطأ {error_id} كمحلول")
                return True
        return False
        
    def cleanup_old_errors(self, days: int = 30):
        """تنظيف الأخطاء القديمة"""
        cutoff_date = datetime.now() - timedelta(days=days)
        
        original_count = len(self.error_history)
        self.error_history = [
            error for error in self.error_history
            if datetime.fromisoformat(error['timestamp']) > cutoff_date
        ]
        
        cleaned_count = original_count - len(self.error_history)
        if cleaned_count > 0:
            self.save_error_history()
            logger.info(f"تم تنظيف {cleaned_count} خطأ قديم")

# مثيل عام لمعالج الأخطاء
error_handler = ErrorHandler()

# Decorators للاستخدام السهل
def handle_errors(max_retries: int = 3, fallback_value: Any = None, 
                 context: Dict[str, Any] = None):
    """
    Decorator لمعالجة الأخطاء تلقائياً
    
    Args:
        max_retries: عدد المحاولات القصوى
        fallback_value: القيمة الاحتياطية
        context: معلومات السياق
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            if max_retries > 0:
                return error_handler.retry_with_backoff(
                    func, *args, max_retries=max_retries, 
                    context=context, **kwargs
                )
            else:
                return error_handler.safe_execute(
                    func, *args, fallback_value=fallback_value,
                    context=context, **kwargs
                )
        return wrapper
    return decorator

def critical_section(context: Dict[str, Any] = None):
    """
    Decorator للأقسام الحرجة التي تتطلب معالجة خاصة للأخطاء
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                error_handler.log_error(e, context, "critical")
                
                # محاولة التعافي
                if error_handler.attempt_recovery(e, context):
                    # إعادة المحاولة بعد التعافي
                    return func(*args, **kwargs)
                else:
                    # إعادة رفع الخطأ إذا فشل التعافي
                    raise
        return wrapper
    return decorator

# استراتيجيات التعافي الافتراضية
def recover_from_connection_error(error: Exception, context: Dict[str, Any] = None) -> bool:
    """استراتيجية التعافي من أخطاء الاتصال"""
    logger.info("محاولة التعافي من خطأ الاتصال...")
    time.sleep(10)  # انتظار 10 ثوان
    return True

def recover_from_memory_error(error: Exception, context: Dict[str, Any] = None) -> bool:
    """استراتيجية التعافي من أخطاء الذاكرة"""
    logger.info("محاولة التعافي من خطأ الذاكرة...")
    import gc
    gc.collect()  # تنظيف الذاكرة
    time.sleep(5)
    return True

# تسجيل استراتيجيات التعافي الافتراضية
error_handler.register_recovery_strategy("ConnectionError", recover_from_connection_error)
error_handler.register_recovery_strategy("MemoryError", recover_from_memory_error)
error_handler.register_recovery_strategy("TimeoutError", recover_from_connection_error)

if __name__ == "__main__":
    # اختبار نظام معالجة الأخطاء
    @handle_errors(max_retries=2)
    def test_function():
        import random
        if random.random() < 0.7:
            raise ConnectionError("اختبار خطأ الاتصال")
        return "نجح!"
        
    result = test_function()
    print(f"نتيجة الاختبار: {result}")
    
    # عرض الإحصائيات
    stats = error_handler.get_error_statistics()
    print(f"إحصائيات الأخطاء: {stats}")
