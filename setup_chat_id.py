#!/usr/bin/env python3
"""
إعداد معرف المحادثة للبوت
يساعد في الحصول على معرف المحادثة من التلغرام
"""

import requests
import json
import time

def get_bot_token():
    """الحصول على رمز البوت من الإعدادات"""
    try:
        import toml
        with open("config.toml", "r", encoding="utf-8") as f:
            config = toml.load(f)
        return config["telegram"]["bot_token"]
    except Exception as e:
        print(f"❌ خطأ في قراءة رمز البوت: {e}")
        return None

def get_chat_id_from_updates(bot_token):
    """الحصول على معرف المحادثة من التحديثات"""
    try:
        url = f"https://api.telegram.org/bot{bot_token}/getUpdates"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data['ok'] and data['result']:
                # البحث عن آخر رسالة
                for update in reversed(data['result']):
                    if 'message' in update:
                        chat_id = str(update['message']['chat']['id'])
                        username = update['message']['from'].get('username', 'غير متوفر')
                        first_name = update['message']['from'].get('first_name', 'غير متوفر')
                        
                        print(f"✅ تم العثور على محادثة:")
                        print(f"   معرف المحادثة: {chat_id}")
                        print(f"   اسم المستخدم: @{username}")
                        print(f"   الاسم الأول: {first_name}")
                        
                        return chat_id
                        
                print("❌ لم يتم العثور على رسائل")
                return None
            else:
                print(f"❌ خطأ في API: {data}")
                return None
        else:
            print(f"❌ خطأ HTTP: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ خطأ في الحصول على التحديثات: {e}")
        return None

def save_chat_id(chat_id):
    """حفظ معرف المحادثة في ملف"""
    try:
        with open("telegram_chat_id.txt", "w") as f:
            f.write(chat_id)
        print(f"✅ تم حفظ معرف المحادثة: {chat_id}")
        return True
    except Exception as e:
        print(f"❌ خطأ في حفظ معرف المحادثة: {e}")
        return False

def test_bot_connection(bot_token):
    """اختبار الاتصال بالبوت"""
    try:
        url = f"https://api.telegram.org/bot{bot_token}/getMe"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data['ok']:
                bot_info = data['result']
                print(f"✅ البوت متصل بنجاح:")
                print(f"   اسم البوت: {bot_info['first_name']}")
                print(f"   اسم المستخدم: @{bot_info['username']}")
                return True
            else:
                print(f"❌ خطأ في البوت: {data}")
                return False
        else:
            print(f"❌ خطأ HTTP: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("""
    🤖 إعداد معرف المحادثة للبوت
    =============================
    
    هذا الأداة ستساعدك في الحصول على معرف المحادثة
    من التلغرام لتفعيل البوت بشكل صحيح.
    
    """)
    
    # الحصول على رمز البوت
    bot_token = get_bot_token()
    if not bot_token:
        print("❌ لا يمكن الحصول على رمز البوت")
        return
    
    print(f"🔑 رمز البوت: {bot_token[:10]}...")
    
    # اختبار الاتصال
    print("\n🧪 اختبار الاتصال بالبوت...")
    if not test_bot_connection(bot_token):
        print("❌ فشل في الاتصال بالبوت")
        return
    
    # البحث عن معرف المحادثة
    print("\n🔍 البحث عن معرف المحادثة...")
    print("💡 إذا لم يتم العثور على معرف، يرجى:")
    print("   1. البحث عن البوت في التلغرام")
    print("   2. إرسال /start أو أي رسالة")
    print("   3. إعادة تشغيل هذا الأداة")
    
    chat_id = get_chat_id_from_updates(bot_token)
    
    if chat_id:
        # حفظ معرف المحادثة
        if save_chat_id(chat_id):
            print("\n🎉 تم إعداد معرف المحادثة بنجاح!")
            print("✅ يمكنك الآن تشغيل البوت بشكل طبيعي")
            
            # اختبار إرسال رسالة
            print("\n🧪 اختبار إرسال رسالة...")
            try:
                url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
                data = {
                    'chat_id': chat_id,
                    'text': '🎉 تم إعداد البوت بنجاح!\n\nيمكنك الآن استخدام /start للوصول للقائمة الرئيسية'
                }
                response = requests.post(url, data=data, timeout=10)
                
                if response.status_code == 200:
                    print("✅ تم إرسال رسالة اختبار بنجاح!")
                else:
                    print(f"⚠️ فشل في إرسال رسالة الاختبار: {response.status_code}")
                    
            except Exception as e:
                print(f"⚠️ خطأ في إرسال رسالة الاختبار: {e}")
        else:
            print("❌ فشل في حفظ معرف المحادثة")
    else:
        print("\n❌ لم يتم العثور على معرف المحادثة")
        print("💡 يرجى:")
        print("   1. البحث عن البوت في التلغرام باستخدام اسم المستخدم")
        print("   2. إرسال /start أو أي رسالة")
        print("   3. إعادة تشغيل هذا الأداة")

if __name__ == "__main__":
    main()
