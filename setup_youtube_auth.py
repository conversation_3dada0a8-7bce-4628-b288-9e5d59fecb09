#!/usr/bin/env python3
"""
سكريبت إعداد مصادقة YouTube OAuth
يساعد في الحصول على التوكن الأولي وإعداد المصادقة
"""

import os
import sys
import argparse
import webbrowser
from pathlib import Path

# إضافة مجلد automation للمسار
sys.path.append(str(Path(__file__).parent / "automation"))

from automation.youtube_uploader import YouTubeTokenManager
from automation.telegram_bot import get_bot

def print_banner():
    """طباعة شعار البرنامج"""
    print("""
╔══════════════════════════════════════════════════════════════╗
║                    🎬 إعداد مصادقة YouTube                    ║
║                   Reddit Video Maker Bot                    ║
╚══════════════════════════════════════════════════════════════╝
    """)

def check_client_secret():
    """التحقق من وجود ملف client_secret.json"""
    client_secret_file = "client_secret.json"
    
    if not os.path.exists(client_secret_file):
        print(f"❌ ملف {client_secret_file} غير موجود!")
        print("\n📋 خطوات الحصول على ملف client_secret.json:")
        print("1. اذهب إلى: https://console.cloud.google.com/")
        print("2. إنشاء مشروع جديد أو اختيار مشروع موجود")
        print("3. تفعيل YouTube Data API v3")
        print("4. إنشاء OAuth 2.0 Client ID")
        print("5. تحميل ملف JSON وإعادة تسميته إلى client_secret.json")
        print("6. وضع الملف في المجلد الرئيسي للبرنامج")
        return False
        
    print(f"✅ تم العثور على ملف {client_secret_file}")
    return True

def setup_initial_auth():
    """إعداد المصادقة الأولية"""
    print("\n🔐 إعداد المصادقة الأولية...")

    # إنشاء مدير التوكن
    token_manager = YouTubeTokenManager()

    # الحصول على رابط المصادقة
    auth_url = token_manager.get_authorization_url()

    if not auth_url:
        print("❌ فشل في إنشاء رابط المصادقة")
        return False

    print(f"\n🌐 رابط المصادقة:")
    print(f"{auth_url}")

    # فتح الرابط في المتصفح
    try:
        webbrowser.open(auth_url)
        print("\n✅ تم فتح الرابط في المتصفح")
    except:
        print("\n⚠️ لم يتم فتح الرابط تلقائياً - انسخ الرابط أعلاه")

    print("\n📋 خطوات المصادقة:")
    print("1. سجل دخولك بحساب Google المرتبط بقناة YouTube")
    print("2. اقبل الأذونات المطلوبة")
    print("3. ستظهر صفحة بها كود المصادقة")
    print("4. انسخ الكود كاملاً")
    print("5. شغل الأمر التالي:")
    print(f"   python {sys.argv[0]} --code YOUR_CODE_HERE")

    print("\n💡 ملاحظة: إذا ظهرت رسالة خطأ في المتصفح، تجاهلها وانسخ الكود من الصفحة")

    return True

def process_authorization_code(code: str):
    """معالجة كود المصادقة"""
    print(f"\n🔄 معالجة كود المصادقة...")
    
    # إنشاء مدير التوكن
    token_manager = YouTubeTokenManager()
    
    # تبديل الكود بالتوكن
    if token_manager.exchange_code_for_token(code):
        print("✅ تم الحصول على التوكن بنجاح!")
        
        # اختبار التوكن
        credentials = token_manager.get_valid_credentials()
        if credentials:
            print("✅ التوكن صالح ويعمل بشكل صحيح")
            
            # إرسال إشعار نجاح عبر Telegram
            try:
                bot = get_bot()
                message = f"""
🎉 *تم إعداد مصادقة YouTube بنجاح!*

✅ تم الحصول على التوكن وحفظه
✅ النظام جاهز لنشر الفيديوهات تلقائياً

🔄 *التجديد التلقائي:* مفعل
⏰ *الوقت:* {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🚀 يمكنك الآن تشغيل النظام التلقائي!

#مصادقة_ناجحة #YouTube #OAuth
                """.strip()
                bot.send_message(message)
                print("✅ تم إرسال إشعار النجاح عبر Telegram")
            except Exception as e:
                print(f"⚠️ فشل في إرسال إشعار Telegram: {e}")
            
            return True
        else:
            print("❌ التوكن غير صالح")
            return False
    else:
        print("❌ فشل في الحصول على التوكن")
        return False

def check_existing_token():
    """التحقق من وجود توكن موجود"""
    token_manager = YouTubeTokenManager()
    credentials = token_manager.get_valid_credentials()
    
    if credentials:
        print("✅ يوجد توكن صالح موجود")
        print("💡 لا حاجة لإعداد مصادقة جديدة")
        return True
    else:
        print("⚠️ لا يوجد توكن صالح")
        return False

def main():
    """الدالة الرئيسية"""
    parser = argparse.ArgumentParser(description="إعداد مصادقة YouTube OAuth")
    parser.add_argument("--code", help="كود المصادقة من Google")
    parser.add_argument("--check", action="store_true", help="التحقق من التوكن الموجود")
    parser.add_argument("--reset", action="store_true", help="إعادة تعيين المصادقة")
    
    args = parser.parse_args()
    
    print_banner()
    
    # التحقق من ملف client_secret
    if not check_client_secret():
        return 1
    
    # التحقق من التوكن الموجود
    if args.check:
        check_existing_token()
        return 0
    
    # إعادة تعيين المصادقة
    if args.reset:
        token_file = "youtube_token.pickle"
        if os.path.exists(token_file):
            os.remove(token_file)
            print(f"✅ تم حذف {token_file}")
        else:
            print(f"⚠️ ملف {token_file} غير موجود")
    
    # معالجة كود المصادقة
    if args.code:
        if process_authorization_code(args.code):
            print("\n🎉 تم إعداد المصادقة بنجاح!")
            print("🚀 يمكنك الآن تشغيل النظام التلقائي")
            return 0
        else:
            print("\n❌ فشل في إعداد المصادقة")
            return 1
    
    # إعداد المصادقة الأولية
    if not check_existing_token():
        if setup_initial_auth():
            return 0
        else:
            return 1
    
    return 0

if __name__ == "__main__":
    try:
        from datetime import datetime
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n⚠️ تم إلغاء العملية بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
