#!/usr/bin/env python3
"""
بوت Telegram المحسن للتحكم الكامل في نظام إنشاء الفيديوهات
يوفر واجهة شاملة مع أزرار تشغيل وإيقاف ومراقبة مستمرة
"""

import asyncio
import logging
import json
import os
import subprocess
import sys
import threading
import time
from datetime import datetime
from typing import Optional, Dict, Any, List
import requests
from pathlib import Path
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, Bot
from telegram.ext import Application, CommandHandler, CallbackQueryHandler, MessageHandler, filters, ContextTypes

# إعداد السجلات
logger = logging.getLogger(__name__)

class EnhancedTelegramBot:
    def __init__(self, bot_token: str = None, chat_id: str = None):
        """
        إعداد بوت Telegram المحسن

        Args:
            bot_token: رمز البوت
            chat_id: معرف المحادثة (سيتم تحديده تلقائياً)
        """
        # استخدام التوكن من الإعدادات إذا لم يتم تمريره
        if not bot_token:
            try:
                from utils import settings
                bot_token = settings.config["telegram"]["bot_token"]
            except:
                bot_token = "7387611581:AAHehFZgbplGEs5IuXXCAdkxWJEtEzxQkT4"

        self.bot_token = bot_token
        self.chat_id = chat_id
        self.base_url = f"https://api.telegram.org/bot{bot_token}"

        # ملف لحفظ معرف المحادثة
        self.chat_id_file = "telegram_chat_id.txt"

        # متغيرات حالة النظام
        self.system_running = False
        self.main_process = None
        self.scheduler_process = None
        self.monitoring_active = False

        # تحميل معرف المحادثة إذا كان محفوظاً
        if not self.chat_id:
            self.chat_id = self._load_chat_id()

        # إعداد البوت
        self._setup_bot()

    def _setup_bot(self):
        """إعداد البوت والتحقق من صحة الرمز"""
        try:
            # التحقق من صحة رمز البوت
            response = requests.get(f"{self.base_url}/getMe", timeout=30)
            if response.status_code == 200:
                bot_info = response.json()
                if bot_info['ok']:
                    logger.info(f"✅ تم إعداد بوت Telegram بنجاح: @{bot_info['result']['username']}")

                    # إرسال رسالة ترحيب إذا كان معرف المحادثة متوفراً
                    if self.chat_id:
                        self.send_startup_message()
                else:
                    raise Exception(f"رمز البوت غير صحيح: {bot_info}")
            else:
                raise Exception(f"فشل في الاتصال بـ Telegram API: {response.status_code}")

        except Exception as e:
            logger.error(f"❌ فشل في إعداد بوت Telegram: {e}")

    async def start_interactive_bot(self):
        """بدء البوت التفاعلي مع الأزرار"""
        try:
            # إنشاء التطبيق
            application = Application.builder().token(self.bot_token).build()

            # إضافة معالجات الأوامر
            application.add_handler(CommandHandler("start", self.start_command))
            application.add_handler(CommandHandler("status", self.status_command))
            application.add_handler(CommandHandler("run", self.run_system_command))
            application.add_handler(CommandHandler("stop", self.stop_system_command))
            application.add_handler(CommandHandler("logs", self.logs_command))
            application.add_handler(CommandHandler("help", self.help_command))
            application.add_handler(CallbackQueryHandler(self.button_callback))

            # بدء البوت
            await application.initialize()
            await application.start()
            await application.updater.start_polling()

            logger.info("🤖 تم بدء البوت التفاعلي بنجاح")

            # إبقاء البوت يعمل
            await application.updater.idle()

        except Exception as e:
            logger.error(f"❌ فشل في بدء البوت التفاعلي: {e}")
            raise

    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """أمر البداية مع الأزرار الرئيسية"""
        try:
            # حفظ معرف المحادثة
            chat_id = str(update.effective_chat.id)
            if not self.chat_id:
                self.chat_id = chat_id
                self._save_chat_id(chat_id)

            # تحديد حالة النظام
            system_status = "🟢 يعمل" if self.system_running else "🔴 متوقف"

            # إنشاء الأزرار
            keyboard = [
                [
                    InlineKeyboardButton("▶️ تشغيل النظام", callback_data="start_system"),
                    InlineKeyboardButton("⏹️ إيقاف النظام", callback_data="stop_system")
                ],
                [
                    InlineKeyboardButton("📊 حالة النظام", callback_data="system_status"),
                    InlineKeyboardButton("📋 السجلات", callback_data="view_logs")
                ],
                [
                    InlineKeyboardButton("🔄 إعادة تشغيل", callback_data="restart_system"),
                    InlineKeyboardButton("⚙️ الإعدادات", callback_data="settings")
                ],
                [
                    InlineKeyboardButton("📈 الإحصائيات", callback_data="statistics"),
                    InlineKeyboardButton("❓ المساعدة", callback_data="help")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            welcome_message = f"""
🤖 **مرحباً بك في نظام إنشاء الفيديوهات التلقائي**

📊 **حالة النظام:** {system_status}

🎯 **المميزات المتاحة:**
• إنشاء فيديوهات تلقائي من Reddit
• نشر على YouTube Shorts
• جدولة ذكية كل 10 ساعات
• مراقبة مستمرة للنظام

استخدم الأزرار أدناه للتحكم في النظام:
            """.strip()

            try:
                await update.message.reply_text(welcome_message, reply_markup=reply_markup, parse_mode='Markdown')
                logger.info("✅ تم إرسال رسالة البداية مع الأزرار بنجاح")
            except Exception as e:
                logger.error(f"❌ فشل في إرسال رسالة البداية: {e}")
                # محاولة إرسال بدون تنسيق
                try:
                    simple_message = f"""
🤖 مرحباً بك في نظام إنشاء الفيديوهات التلقائي

📊 حالة النظام: {system_status}

🎯 المميزات المتاحة:
• إنشاء فيديوهات تلقائي من Reddit
• نشر على YouTube Shorts
• جدولة ذكية كل 10 ساعات
• مراقبة مستمرة للنظام

استخدم الأزرار أدناه للتحكم في النظام:
                    """.strip()
                    await update.message.reply_text(simple_message, reply_markup=reply_markup)
                    logger.info("✅ تم إرسال رسالة البداية بدون تنسيق")
                except Exception as e2:
                    logger.error(f"❌ فشل في إرسال رسالة البداية حتى بدون تنسيق: {e2}")
                    raise

        except Exception as e:
            logger.error(f"خطأ في أمر البداية: {e}")
            await update.message.reply_text("❌ حدث خطأ في تحميل القائمة الرئيسية")

    def _load_chat_id(self) -> Optional[str]:
        """تحميل معرف المحادثة من الملف"""
        try:
            if os.path.exists(self.chat_id_file):
                with open(self.chat_id_file, 'r') as f:
                    chat_id = f.read().strip()
                    if chat_id:
                        logger.info(f"تم تحميل معرف المحادثة: {chat_id}")
                        return chat_id
        except Exception as e:
            logger.warning(f"فشل في تحميل معرف المحادثة: {e}")
        return None

    def _save_chat_id(self, chat_id: str):
        """حفظ معرف المحادثة في ملف"""
        try:
            with open(self.chat_id_file, 'w') as f:
                f.write(chat_id)
            logger.info(f"تم حفظ معرف المحادثة: {chat_id}")
        except Exception as e:
            logger.error(f"فشل في حفظ معرف المحادثة: {e}")

    async def button_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج أزرار الكيبورد"""
        query = update.callback_query
        await query.answer()

        try:
            data = query.data

            if data == "start_system":
                await self._handle_start_system(query)
            elif data == "stop_system":
                await self._handle_stop_system(query)
            elif data == "restart_system":
                await self._handle_restart_system(query)
            elif data == "system_status":
                await self._handle_system_status(query)
            elif data == "view_logs":
                await self._handle_view_logs(query)
            elif data == "settings":
                await self._handle_settings(query)
            elif data == "statistics":
                await self._handle_statistics(query)
            elif data == "help":
                await self._handle_help(query)
            else:
                await query.edit_message_text("❌ أمر غير معروف")

        except Exception as e:
            logger.error(f"خطأ في معالج الأزرار: {e}")
            await query.edit_message_text("❌ حدث خطأ في معالجة الطلب")

    async def _handle_start_system(self, query):
        """معالجة تشغيل النظام"""
        try:
            if self.system_running:
                await query.edit_message_text("🟢 النظام يعمل بالفعل!")
                return

            await query.edit_message_text("🚀 بدء تشغيل النظام...")

            # تشغيل النظام في عملية منفصلة
            self.main_process = subprocess.Popen([
                sys.executable, "main.py"
            ], cwd=os.getcwd(),
               stdout=subprocess.PIPE,
               stderr=subprocess.PIPE,
               text=True)

            self.system_running = True

            # بدء مراقبة العملية
            threading.Thread(target=self._monitor_system_process, daemon=True).start()

            success_message = """
✅ **تم تشغيل النظام بنجاح!**

🆔 معرف العملية: {}
📊 سيتم إرسال تحديثات الحالة تلقائياً
⚠️ ستصلك إشعارات عند وجود مشاكل

استخدم الأزرار للتحكم في النظام
            """.format(self.main_process.pid).strip()

            await query.edit_message_text(success_message, parse_mode='Markdown')

        except Exception as e:
            logger.error(f"فشل في تشغيل النظام: {e}")
            await query.edit_message_text(f"❌ فشل في تشغيل النظام: {str(e)}")

    def get_updates(self) -> Dict[str, Any]:
        """الحصول على التحديثات من Telegram"""
        try:
            response = requests.get(f"{self.base_url}/getUpdates", timeout=30)
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"فشل في الحصول على التحديثات: {response.status_code}")
                return {}
        except Exception as e:
            logger.error(f"خطأ في الحصول على التحديثات: {e}")
            return {}

    def auto_detect_chat_id(self):
        """اكتشاف معرف المحادثة تلقائياً من آخر رسالة"""
        try:
            updates = self.get_updates()
            if updates.get('ok') and updates.get('result'):
                # البحث عن آخر رسالة
                for update in reversed(updates['result']):
                    if 'message' in update:
                        chat_id = str(update['message']['chat']['id'])
                        self.chat_id = chat_id
                        self._save_chat_id(chat_id)
                        logger.info(f"تم اكتشاف معرف المحادثة تلقائياً: {chat_id}")
                        return True

            logger.warning("لم يتم العثور على رسائل لاكتشاف معرف المحادثة")
            return False

        except Exception as e:
            logger.error(f"فشل في اكتشاف معرف المحادثة: {e}")
            return False

    async def _handle_stop_system(self, query):
        """معالجة إيقاف النظام"""
        try:
            if not self.system_running:
                await query.edit_message_text("🔴 النظام متوقف بالفعل!")
                return

            await query.edit_message_text("⏹️ إيقاف النظام...")

            if self.main_process:
                self.main_process.terminate()
                self.main_process.wait(timeout=10)
                self.main_process = None

            self.system_running = False
            await query.edit_message_text("✅ تم إيقاف النظام بنجاح!")

        except Exception as e:
            logger.error(f"فشل في إيقاف النظام: {e}")
            await query.edit_message_text(f"❌ فشل في إيقاف النظام: {str(e)}")

    async def _handle_restart_system(self, query):
        """معالجة إعادة تشغيل النظام"""
        try:
            await query.edit_message_text("🔄 إعادة تشغيل النظام...")

            # إيقاف النظام أولاً
            if self.main_process:
                self.main_process.terminate()
                self.main_process.wait(timeout=10)

            # تشغيل النظام مرة أخرى
            self.main_process = subprocess.Popen([
                sys.executable, "main.py"
            ], cwd=os.getcwd(),
               stdout=subprocess.PIPE,
               stderr=subprocess.PIPE,
               text=True)

            self.system_running = True
            threading.Thread(target=self._monitor_system_process, daemon=True).start()

            await query.edit_message_text("✅ تم إعادة تشغيل النظام بنجاح!")

        except Exception as e:
            logger.error(f"فشل في إعادة تشغيل النظام: {e}")
            await query.edit_message_text(f"❌ فشل في إعادة تشغيل النظام: {str(e)}")

    def _monitor_system_process(self):
        """مراقبة عملية النظام"""
        try:
            while self.system_running and self.main_process:
                # فحص حالة العملية
                if self.main_process.poll() is not None:
                    # العملية انتهت
                    self.system_running = False

                    # إرسال إشعار
                    if self.main_process.returncode == 0:
                        self.send_message("✅ **تم إنشاء الفيديو بنجاح!**\n\n🎉 العملية اكتملت بدون أخطاء")
                    else:
                        self.send_message(f"❌ **توقف النظام بشكل غير متوقع!**\n\nكود الخروج: {self.main_process.returncode}")
                    break

                time.sleep(30)  # فحص كل 30 ثانية

        except Exception as e:
            logger.error(f"خطأ في مراقبة العملية: {e}")

    def send_message(self, message: str, parse_mode: str = "Markdown") -> bool:
        """
        إرسال رسالة عبر Telegram

        Args:
            message: نص الرسالة
            parse_mode: تنسيق الرسالة (Markdown أو HTML)

        Returns:
            True إذا تم الإرسال بنجاح
        """
        try:
            # إذا لم يكن معرف المحادثة متوفراً أو كان القيمة الافتراضية، حاول اكتشافه
            if not self.chat_id or self.chat_id == "YOUR_CHAT_ID_HERE":
                if not self.auto_detect_chat_id():
                    logger.warning("لا يمكن إرسال الرسالة: معرف المحادثة غير متوفر")
                    print("💡 لإعداد معرف المحادثة:")
                    print("   1. ابحث عن البوت: @sah8fqwuhfu_bot")
                    print("   2. أرسل /start")
                    print("   3. شغل: python get_chat_id.py")
                    return False

            # تنظيف الرسالة من الرموز التي قد تسبب مشاكل في التحليل
            clean_message = self._clean_message_for_telegram(message)

            # تحضير البيانات
            data = {
                'chat_id': self.chat_id,
                'text': clean_message[:4096],  # حد أقصى 4096 حرف
                'parse_mode': parse_mode
            }

            # إرسال الرسالة
            response = requests.post(f"{self.base_url}/sendMessage", data=data, timeout=30)

            if response.status_code == 200:
                result = response.json()
                if result['ok']:
                    logger.debug("تم إرسال رسالة Telegram بنجاح")
                    return True
                else:
                    logger.error(f"فشل في إرسال رسالة Telegram: {result}")
                    return False
            else:
                try:
                    error_details = response.json()
                    logger.error(f"خطأ HTTP في إرسال رسالة Telegram: {response.status_code}")
                    logger.error(f"تفاصيل الخطأ: {error_details}")

                    # إذا كان الخطأ 400، قد يكون بسبب تنسيق الرسالة
                    if response.status_code == 400:
                        logger.warning("محاولة إرسال الرسالة بدون تنسيق Markdown...")
                        # إعادة المحاولة بدون تنسيق
                        simple_data = {
                            'chat_id': self.chat_id,
                            'text': message[:4096]
                        }
                        retry_response = requests.post(f"{self.base_url}/sendMessage", data=simple_data, timeout=30)
                        if retry_response.status_code == 200:
                            logger.info("تم إرسال الرسالة بنجاح بدون تنسيق")
                            return True
                except:
                    pass
                return False

        except Exception as e:
            logger.error(f"خطأ في إرسال رسالة Telegram: {e}")
            return False

    def _clean_message_for_telegram(self, message: str) -> str:
        """تنظيف الرسالة من الرموز التي قد تسبب مشاكل في التحليل"""
        import re

        # إزالة الرموز التي قد تسبب مشاكل في Markdown
        # استبدال الرموز الخاصة بـ Markdown
        message = re.sub(r'([_*\[\]()~`>#+\-=|{}.!])', r'\\\1', message)

        # إزالة الأحرف غير المطبوعة
        message = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', message)

        # إزالة المسافات الزائدة
        message = re.sub(r'\s+', ' ', message).strip()

        return message

    def send_startup_message(self):
        """إرسال رسالة بدء التشغيل"""
        message = f"""
🚀 **تم تشغيل نظام إنشاء الفيديوهات**

🕐 الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

⚙️ **الإعدادات:**
• الجدولة: كل 10 ساعات
• المنصة: YouTube Shorts
• المصدر: Reddit
• الذكاء الاصطناعي: Gemini AI

✅ النظام جاهز للعمل!

استخدم /start للوصول للقائمة الرئيسية
        """.strip()

        return self.send_message(message)

    async def status_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض حالة النظام التفصيلية"""
        try:
            status_message = self._get_system_status()
            await update.message.reply_text(status_message, parse_mode='Markdown')
        except Exception as e:
            logger.error(f"خطأ في عرض الحالة: {e}")
            await update.message.reply_text("❌ حدث خطأ في عرض حالة النظام")

    async def run_system_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """أمر تشغيل النظام"""
        if self.system_running:
            await update.message.reply_text("🟢 النظام يعمل بالفعل!")
            return

        await update.message.reply_text("🚀 بدء تشغيل النظام...")
        # استخدام نفس منطق _handle_start_system

    async def stop_system_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """أمر إيقاف النظام"""
        if not self.system_running:
            await update.message.reply_text("🔴 النظام متوقف بالفعل!")
            return

        await update.message.reply_text("⏹️ إيقاف النظام...")
        # استخدام نفس منطق _handle_stop_system

    async def logs_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض آخر السجلات"""
        try:
            logs = self._get_recent_logs()
            await update.message.reply_text(f"📋 **آخر السجلات:**\n\n```\n{logs}\n```", parse_mode='Markdown')
        except Exception as e:
            logger.error(f"خطأ في عرض السجلات: {e}")
            await update.message.reply_text("❌ حدث خطأ في عرض السجلات")

    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض المساعدة"""
        help_text = """
🤖 **مساعدة نظام إنشاء الفيديوهات**

**الأوامر المتاحة:**
• `/start` - القائمة الرئيسية مع الأزرار
• `/status` - حالة النظام التفصيلية
• `/run` - تشغيل النظام
• `/stop` - إيقاف النظام
• `/logs` - عرض آخر السجلات
• `/help` - هذه المساعدة

**الأزرار التفاعلية:**
• ▶️ تشغيل النظام
• ⏹️ إيقاف النظام
• 🔄 إعادة تشغيل
• 📊 حالة النظام
• 📋 السجلات
• ⚙️ الإعدادات
• 📈 الإحصائيات

**المميزات:**
✅ إنشاء فيديوهات تلقائي من Reddit
✅ نشر على YouTube Shorts
✅ جدولة ذكية كل 10 ساعات
✅ مراقبة مستمرة للنظام
✅ إشعارات فورية للأخطاء والنجاح
        """.strip()

        await update.message.reply_text(help_text, parse_mode='Markdown')

    def _get_system_status(self) -> str:
        """الحصول على حالة النظام التفصيلية"""
        status = "🔴 متوقف" if not self.system_running else "🟢 يعمل"
        process_info = ""

        if self.main_process:
            if self.main_process.poll() is None:
                process_info = f"\n🆔 معرف العملية: {self.main_process.pid}"
            else:
                process_info = "\n⚠️ العملية انتهت"

        return f"""
📊 **حالة النظام التفصيلية**

🤖 **الحالة:** {status}{process_info}
🕐 **الوقت:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

⚙️ **المكونات:**
• مولد المحتوى: Gemini AI
• منصة النشر: YouTube Shorts
• الإشعارات: Telegram Bot ✅
• المراقبة: نشطة ✅

📈 **الإحصائيات:**
• آخر تشغيل: {datetime.now().strftime('%H:%M')}
• حالة الاتصال: متصل ✅
        """.strip()

    def _get_recent_logs(self, lines: int = 20) -> str:
        """الحصول على آخر السجلات"""
        try:
            log_file = Path("logs/main.log")
            if log_file.exists():
                with open(log_file, 'r', encoding='utf-8') as f:
                    all_lines = f.readlines()
                    recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
                    return ''.join(recent_lines)
            else:
                return "لا توجد سجلات متاحة"
        except Exception as e:
            return f"خطأ في قراءة السجلات: {e}"

    async def _handle_system_status(self, query):
        """معالجة عرض حالة النظام"""
        try:
            status_message = self._get_system_status()
            await query.edit_message_text(status_message, parse_mode='Markdown')
        except Exception as e:
            logger.error(f"خطأ في عرض الحالة: {e}")
            await query.edit_message_text("❌ حدث خطأ في عرض حالة النظام")

    async def _handle_view_logs(self, query):
        """معالجة عرض السجلات"""
        try:
            logs = self._get_recent_logs()
            await query.edit_message_text(f"📋 **آخر السجلات:**\n\n```\n{logs}\n```", parse_mode='Markdown')
        except Exception as e:
            logger.error(f"خطأ في عرض السجلات: {e}")
            await query.edit_message_text("❌ حدث خطأ في عرض السجلات")

    async def _handle_settings(self, query):
        """معالجة الإعدادات"""
        try:
            settings_keyboard = [
                [InlineKeyboardButton("🎤 إعدادات الصوت", callback_data="audio_settings")],
                [InlineKeyboardButton("🎬 إعدادات الفيديو", callback_data="video_settings")],
                [InlineKeyboardButton("📱 إعدادات التلغرام", callback_data="telegram_settings")],
                [InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="main_menu")]
            ]
            reply_markup = InlineKeyboardMarkup(settings_keyboard)

            await query.edit_message_text(
                "⚙️ **إعدادات النظام**\n\nاختر الإعدادات التي تريد تعديلها:",
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )
        except Exception as e:
            logger.error(f"خطأ في عرض الإعدادات: {e}")
            await query.edit_message_text("❌ حدث خطأ في عرض الإعدادات")

    async def _handle_statistics(self, query):
        """معالجة الإحصائيات"""
        try:
            stats = self._get_system_statistics()
            await query.edit_message_text(stats, parse_mode='Markdown')
        except Exception as e:
            logger.error(f"خطأ في عرض الإحصائيات: {e}")
            await query.edit_message_text("❌ حدث خطأ في عرض الإحصائيات")

    async def _handle_help(self, query):
        """معالجة المساعدة"""
        help_text = """
🤖 **مساعدة نظام إنشاء الفيديوهات**

**الأزرار المتاحة:**
▶️ **تشغيل النظام** - بدء إنشاء فيديو جديد
⏹️ **إيقاف النظام** - إيقاف العمليات الجارية
🔄 **إعادة تشغيل** - إعادة تشغيل النظام
📊 **حالة النظام** - عرض الحالة التفصيلية
📋 **السجلات** - عرض آخر السجلات
⚙️ **الإعدادات** - إدارة إعدادات النظام
📈 **الإحصائيات** - عرض الإحصائيات

**الأوامر النصية:**
• `/start` - القائمة الرئيسية
• `/run` - تشغيل النظام
• `/stop` - إيقاف النظام
• `/status` - حالة النظام
• `/logs` - عرض السجلات
• `/help` - هذه المساعدة

**المميزات:**
✅ إنشاء فيديوهات تلقائي من Reddit
✅ نشر على YouTube Shorts
✅ جدولة ذكية كل 10 ساعات
✅ مراقبة مستمرة للنظام
✅ إشعارات فورية للأخطاء والنجاح
        """.strip()

        await query.edit_message_text(help_text, parse_mode='Markdown')

    def _get_system_statistics(self) -> str:
        """الحصول على إحصائيات النظام"""
        try:
            # إحصائيات أساسية
            stats = f"""
📈 **إحصائيات النظام**

🕐 **الوقت:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

📊 **الإحصائيات العامة:**
• حالة النظام: {"🟢 يعمل" if self.system_running else "🔴 متوقف"}
• وقت التشغيل: {datetime.now().strftime('%H:%M')}
• آخر تحديث: {datetime.now().strftime('%H:%M:%S')}

💾 **الموارد:**
• استخدام الذاكرة: متاح
• مساحة القرص: متاح
• حالة الاتصال: متصل ✅

🎬 **الفيديوهات:**
• آخر إنشاء: غير متوفر
• العدد الإجمالي: غير متوفر
• معدل النجاح: غير متوفر

📱 **التلغرام:**
• حالة البوت: نشط ✅
• آخر رسالة: {datetime.now().strftime('%H:%M:%S')}
            """.strip()

            return stats

        except Exception as e:
            return f"❌ خطأ في جمع الإحصائيات: {e}"

    def send_video_notification(self, video_path: str, video_url: str,
                               title: str, description: str) -> bool:
        """إرسال إشعار نجاح نشر فيديو"""
        try:
            # إحصائيات الملف
            file_size = os.path.getsize(video_path) / (1024 * 1024)  # بالميجابايت
            
            message = f"""
🎉 *تم نشر فيديو جديد بنجاح!*

📹 *العنوان:* {title}

📝 *الوصف:* {description[:200]}{'...' if len(description) > 200 else ''}

🔗 *رابط YouTube:* {video_url}

📊 *معلومات الملف:*
• الحجم: {file_size:.1f} MB
• الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

#نشر_ناجح #YouTube #Shorts
            """.strip()
            
            return self.send_message(message)
            
        except Exception as e:
            logger.error(f"خطأ في إرسال إشعار الفيديو: {e}")
            return False
            
    def send_error_notification(self, error_message: str, error_details: str = None) -> bool:
        """إرسال إشعار خطأ"""
        try:
            message = f"""
❌ *حدث خطأ في النظام*

🕐 *الوقت:* {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

⚠️ *الخطأ:* {error_message}
            """.strip()
            
            if error_details:
                message += f"\n\n📋 *التفاصيل:*\n```\n{error_details[:1000]}\n```"
                
            return self.send_message(message)
            
        except Exception as e:
            logger.error(f"خطأ في إرسال إشعار الخطأ: {e}")
            return False
            
    def send_status_report(self, stats: Dict[str, Any]) -> bool:
        """إرسال تقرير حالة النظام"""
        try:
            message = f"""
📊 *تقرير حالة النظام*

🕐 *الوقت:* {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

📈 *الإحصائيات:*
• الفيديوهات المنشورة: {stats.get('videos_published', 0)}
• آخر نشر: {stats.get('last_publish', 'غير متوفر')}
• الأخطاء: {stats.get('error_count', 0)}
• وقت التشغيل: {stats.get('uptime', 'غير متوفر')}

💾 *الموارد:*
• استخدام الذاكرة: {stats.get('memory_usage', 'غير متوفر')}%
• مساحة القرص: {stats.get('disk_usage', 'غير متوفر')}%

✅ النظام يعمل بشكل طبيعي
            """.strip()
            
            return self.send_message(message)
            
        except Exception as e:
            logger.error(f"خطأ في إرسال تقرير الحالة: {e}")
            return False
            
    def send_startup_notification(self) -> bool:
        """إرسال إشعار بدء التشغيل"""
        message = f"""
🚀 *تم بدء تشغيل نظام إنشاء الفيديوهات*

🕐 *الوقت:* {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

⚙️ *الإعدادات:*
• الجدولة: كل 10 ساعات
• المنصة: YouTube Shorts
• المصدر: Reddit
• الذكاء الاصطناعي: Gemini 2.5 Pro

✅ النظام جاهز للعمل!
        """.strip()
        
        return self.send_message(message)
        
    def send_shutdown_notification(self) -> bool:
        """إرسال إشعار إيقاف النظام"""
        message = f"""
🛑 *تم إيقاف نظام إنشاء الفيديوهات*

🕐 *الوقت:* {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

ℹ️ النظام متوقف حالياً
        """.strip()

        return self.send_message(message)

    def send_token_expiry_warning(self, hours_until_expiry: int) -> bool:
        """إرسال تحذير انتهاء صلاحية التوكن"""
        try:
            message = f"""
⚠️ *تحذير: انتهاء صلاحية توكن YouTube قريباً*

⏰ *الوقت المتبقي:* {hours_until_expiry} ساعة

🕐 *الوقت الحالي:* {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🔄 *الإجراء:*
سيتم محاولة تجديد التوكن تلقائياً عند الحاجة.

💡 *نصيحة:* إذا فشل التجديد التلقائي، ستحتاج لمصادقة جديدة.

#تحذير_التوكن #YouTube #OAuth
            """.strip()

            return self.send_message(message)

        except Exception as e:
            logger.error(f"خطأ في إرسال تحذير انتهاء التوكن: {e}")
            return False

    def send_token_refresh_success(self) -> bool:
        """إرسال إشعار نجاح تجديد التوكن"""
        try:
            message = f"""
✅ *تم تجديد توكن YouTube بنجاح*

🕐 *الوقت:* {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🔄 *التجديد:* تلقائي
⏳ *صالح لمدة:* ساعة واحدة تقريباً

✅ النظام يعمل بشكل طبيعي

#تجديد_ناجح #YouTube #OAuth
            """.strip()

            return self.send_message(message)

        except Exception as e:
            logger.error(f"خطأ في إرسال إشعار نجاح التجديد: {e}")
            return False

    def send_token_refresh_failed(self, error_details: str = None) -> bool:
        """إرسال إشعار فشل تجديد التوكن"""
        try:
            message = f"""
❌ *فشل في تجديد توكن YouTube*

🕐 *الوقت:* {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

⚠️ *المشكلة:* انتهت صلاحية refresh token أو حدث خطأ

📋 *الإجراء المطلوب:*
1. شغل الأمر التالي:
```
python setup_youtube_auth.py
```

2. اتبع التعليمات للحصول على مصادقة جديدة

3. أعد تشغيل النظام

🔗 *أو استخدم الرابط المباشر للمصادقة*

#فشل_التجديد #YouTube #مصادقة_مطلوبة
            """.strip()

            if error_details:
                message += f"\n\n📋 *تفاصيل الخطأ:*\n```\n{error_details[:500]}\n```"

            return self.send_message(message)

        except Exception as e:
            logger.error(f"خطأ في إرسال إشعار فشل التجديد: {e}")
            return False

    def send_auth_setup_instructions(self, auth_url: str = None) -> bool:
        """إرسال تعليمات إعداد المصادقة"""
        try:
            message = f"""
🔐 *مطلوب إعداد مصادقة YouTube جديدة*

🕐 *الوقت:* {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

📋 *خطوات الإعداد:*

**الطريقة الأولى - التلقائية:**
```
python setup_youtube_auth.py
```

**الطريقة الثانية - اليدوية:**
1. شغل الأمر:
```
python setup_youtube_auth.py
```

2. اتبع الرابط الذي سيظهر

3. سجل دخولك بحساب Google

4. انسخ الكود من الرابط النهائي

5. شغل:
```
python setup_youtube_auth.py --code YOUR_CODE
```

💡 *نصيحة:* تأكد من استخدام نفس حساب Google المرتبط بقناة YouTube

#مصادقة_مطلوبة #YouTube #إعداد
            """.strip()

            if auth_url:
                message += f"\n\n🔗 *رابط المصادقة المباشر:*\n{auth_url}"

            return self.send_message(message)

        except Exception as e:
            logger.error(f"خطأ في إرسال تعليمات المصادقة: {e}")
            return False

# متغير عام للبوت
_bot_instance = None

def get_bot() -> EnhancedTelegramBot:
    """الحصول على مثيل البوت (Singleton)"""
    global _bot_instance
    if _bot_instance is None:
        _bot_instance = EnhancedTelegramBot()
    return _bot_instance

# دوال مساعدة للاستخدام السهل
def send_notification(message: str) -> bool:
    """دالة مساعدة لإرسال إشعار"""
    try:
        bot = get_bot()
        return bot.send_message(message)
    except Exception as e:
        logger.error(f"خطأ في إرسال الإشعار: {e}")
        return False

def send_video_success(video_path: str, video_url: str, title: str, description: str) -> bool:
    """دالة مساعدة لإرسال إشعار نجاح الفيديو"""
    try:
        bot = get_bot()
        return bot.send_video_notification(video_path, video_url, title, description)
    except Exception as e:
        logger.error(f"خطأ في إرسال إشعار نجاح الفيديو: {e}")
        return False

def send_error(error_message: str, error_details: str = None) -> bool:
    """دالة مساعدة لإرسال إشعار خطأ"""
    try:
        bot = get_bot()
        return bot.send_error_notification(error_message, error_details)
    except Exception as e:
        logger.error(f"خطأ في إرسال إشعار الخطأ: {e}")
        return False

def send_token_expiry_warning(hours_until_expiry: int) -> bool:
    """دالة مساعدة لإرسال تحذير انتهاء التوكن"""
    try:
        bot = get_bot()
        return bot.send_token_expiry_warning(hours_until_expiry)
    except Exception as e:
        logger.error(f"خطأ في إرسال تحذير انتهاء التوكن: {e}")
        return False

def send_token_refresh_success() -> bool:
    """دالة مساعدة لإرسال إشعار نجاح تجديد التوكن"""
    try:
        bot = get_bot()
        return bot.send_token_refresh_success()
    except Exception as e:
        logger.error(f"خطأ في إرسال إشعار نجاح التجديد: {e}")
        return False

def send_token_refresh_failed(error_details: str = None) -> bool:
    """دالة مساعدة لإرسال إشعار فشل تجديد التوكن"""
    try:
        bot = get_bot()
        return bot.send_token_refresh_failed(error_details)
    except Exception as e:
        logger.error(f"خطأ في إرسال إشعار فشل التجديد: {e}")
        return False

def send_auth_setup_instructions(auth_url: str = None) -> bool:
    """دالة مساعدة لإرسال تعليمات إعداد المصادقة"""
    try:
        bot = get_bot()
        return bot.send_auth_setup_instructions(auth_url)
    except Exception as e:
        logger.error(f"خطأ في إرسال تعليمات المصادقة: {e}")
        return False

if __name__ == "__main__":
    # اختبار البوت
    bot = EnhancedTelegramBot()

    # اختبار إرسال رسالة
    success = bot.send_message("🧪 اختبار بوت Telegram")
    print(f"نتيجة الاختبار: {success}")

    # اختبار إشعار بدء التشغيل
    bot.send_startup_notification()
