{% extends "layout.html" %}
{% block main %}

<main>
    <div class="album py-2 bg-light">
        <div class="container">

            <div class="row mt-2">
                <div class="col-12 col-md-3 mb-3">
                    <input type="text" class="form-control searchFilter" placeholder="Search videos"
                        aria-label="Search videos" onkeyup="searchFilter()">
                </div>
            </div>

            <div class="grid row row-cols-1 row-cols-sm-2 row-cols-md-3 g-3" id="videos">

            </div>
        </div>
    </div>
</main>

<script>
    const intervals = [
        { label: 'year', seconds: 31536000 },
        { label: 'month', seconds: 2592000 },
        { label: 'day', seconds: 86400 },
        { label: 'hour', seconds: 3600 },
        { label: 'minute', seconds: 60 },
        { label: 'second', seconds: 1 }
    ];

    function timeSince(date) {
        const seconds = Math.floor((Date.now() / 1000 - date));
        const interval = intervals.find(i => i.seconds < seconds);
        const count = Math.floor(seconds / interval.seconds);
        return `${count} ${interval.label}${count !== 1 ? 's' : ''} ago`;
    }

    $(document).ready(function () {
        $.getJSON("videos.json",
            function (data) {
                data.sort((b, a) => a['time'] - b['time'])
                var video = '';
                $.each(data, function (key, value) {
                    video += '<div class="col">';
                    video += '<div class="card shadow-sm">';
                    //keeping original themed image card for future thumbnail usage video += '<svg class="bd-placeholder-img card-img-top" width="100%" height="225" xmlns="http://www.w3.org/2000/svg" role="img" aria-label="Placeholder: Thumbnail" preserveAspectRatio="xMidYMid slice" focusable="false"><title>Placeholder</title><rect width="100%" height="100%" fill="#55595c"/><text x="50%" y="50%" fill="#eceeef" dy=".3em">r/'+value.subreddit+'</text></svg>';

                    video += '<div class="card-body">';
                    video += '<p class="card-text">r/' + value.subreddit + ' • ' + checkTitle(value.reddit_title, value.filename) + '</p>';
                    video += '<div class="d-flex justify-content-between align-items-center">';
                    video += '<div class="btn-group">';
                    video += '<a href="https://www.reddit.com/r/' + value.subreddit + '/comments/' + value.id + '/" class="btn btn-sm btn-outline-secondary" target="_blank">View</a>';
                    video += '<a href="http://localhost:4000/results/' + value.subreddit + '/' + value.filename + '" class="btn btn-sm btn-outline-secondary" download>Download</a>';
                    video += '</div>';
                    video += '<div class="btn-group">';
                    video += '<button type="button" data-toggle="tooltip" id="copy" data-original-title="Copy to clipboard" class="btn btn-sm btn-outline-secondary" data-clipboard-text="' + getCopyData(value.subreddit, value.reddit_title, value.filename, value.background_credit) + '"><i class="bi bi-card-text"></i></button>';
                    video += '<button type="button" data-toggle="tooltip" id="copy" data-original-title="Copy to clipboard" class="btn btn-sm btn-outline-secondary" data-clipboard-text="' + checkTitle(value.reddit_title, value.filename) + ' #Shorts #reddit"><i class="bi bi-youtube"></i></button>';
                    video += '<button type="button" data-toggle="tooltip" id="copy" data-original-title="Copy to clipboard" class="btn btn-sm btn-outline-secondary" data-clipboard-text="' + checkTitle(value.reddit_title, value.filename) + ' #reddit"><i class="bi bi-instagram"></i></button>';
                    video += '</div>';
                    video += '<small class="text-muted">' + timeSince(value.time) + '</small>';
                    video += '</div>';
                    video += '</div>';
                    video += '</div>';
                    video += '</div>';

                });

                $('#videos').append(video);
            });
    });

    $(document).ready(function () {
        $('[data-toggle="tooltip"]').tooltip();
        $('[data-toggle="tooltip"]').on('click', function () {
            $(this).tooltip('hide');
        });
    });

    $('#copy').tooltip({
        trigger: 'click',
        placement: 'bottom'
    });

    function setTooltip(btn, message) {
        $(btn).tooltip('hide')
            .attr('data-original-title', message)
            .tooltip('show');
    }

    function hoverTooltip(btn, message) {
        $(btn).tooltip('hide')
            .attr('data-original-title', message)
            .tooltip('show');
    }

    function hideTooltip(btn) {
        setTimeout(function () {
            $(btn).tooltip('hide');
        }, 1000);
    }

    function disposeTooltip(btn) {
        setTimeout(function () {
            $(btn).tooltip('dispose');
        }, 1500);
    }

    var clipboard = new ClipboardJS('#copy');

    clipboard.on('success', function (e) {
        e.clearSelection();
        console.info('Action:', e.action);
        console.info('Text:', e.text);
        console.info('Trigger:', e.trigger);
        setTooltip(e.trigger, 'Copied!');
        hideTooltip(e.trigger);
        disposeTooltip(e.trigger);
    });

    clipboard.on('error', function (e) {
        console.error('Action:', e.action);
        console.error('Trigger:', e.trigger);
        setTooltip(e.trigger, fallbackMessage(e.action));
        hideTooltip(e.trigger);
    });

    function getCopyData(subreddit, reddit_title, filename, background_credit) {

        if (subreddit == undefined) {
            subredditCopy = "";
        } else {
            subredditCopy = "r/" + subreddit + "\n\n";
        }

        const file = filename.slice(0, -4);
        if (reddit_title == file) {
            titleCopy = reddit_title;
        } else {
            titleCopy = file;
        }

        var copyData = "";
        copyData += subredditCopy;
        copyData += titleCopy;
        copyData += "\n\nBackground credit: " + background_credit;
        return copyData;
    }

    function getLink(subreddit, id, reddit_title) {
        if (subreddit == undefined) {
            return reddit_title;
        } else {
            return "<a target='_blank' href='https://www.reddit.com/r/" + subreddit + "/comments/" + id + "/'>" + reddit_title + "</a>";
        }
    }

    function checkTitle(reddit_title, filename) {
        const file = filename.slice(0, -4);
        if (reddit_title == file) {
            return reddit_title;
        } else {
            return file;
        }
    }

    var searchFilter = () => {
        const input = document.querySelector(".searchFilter");
        const cards = document.getElementsByClassName("col");
        console.log(cards[1])
        let filter = input.value
        for (let i = 0; i < cards.length; i++) {
            let title = cards[i].querySelector(".card-text");
            if (title.innerText.toLowerCase().indexOf(filter.toLowerCase()) > -1) {
                cards[i].classList.remove("d-none")
            } else {
                cards[i].classList.add("d-none")
            }
        }
    }
</script>
{% endblock %}