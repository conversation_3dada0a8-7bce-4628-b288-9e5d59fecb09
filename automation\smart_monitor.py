#!/usr/bin/env python3
"""
نظام مراقبة ذكي للبوت
يراقب الأخطاء ويتخذ إجراءات تلقائية للإصلاح
"""

import logging
import time
import json
import asyncio
from typing import Dict, List, Optional
from pathlib import Path
from datetime import datetime, timedelta

from automation.telegram_bot import send_error, send_notification
from utils import settings

logger = logging.getLogger(__name__)

class SmartMonitor:
    """مراقب ذكي للنظام"""
    
    def __init__(self):
        self.error_counts = {}
        self.last_errors = {}
        self.recovery_attempts = {}
        self.system_health = {
            "tts_engines": {},
            "reddit_connection": True,
            "telegram_bot": True,
            "last_successful_video": None
        }
        
        # إعدادات المراقبة
        self.max_errors_per_hour = 5
        self.recovery_cooldown = 300  # 5 دقائق
        self.health_check_interval = 60  # دقيقة واحدة
        
    def log_error(self, error_type: str, error_message: str, context: Dict = None):
        """تسجيل خطأ مع تحليل ذكي"""
        timestamp = time.time()
        
        # تسجيل الخطأ
        if error_type not in self.error_counts:
            self.error_counts[error_type] = []
        
        self.error_counts[error_type].append(timestamp)
        self.last_errors[error_type] = {
            "message": error_message,
            "timestamp": timestamp,
            "context": context or {}
        }
        
        # تنظيف الأخطاء القديمة (أكثر من ساعة)
        hour_ago = timestamp - 3600
        self.error_counts[error_type] = [
            t for t in self.error_counts[error_type] if t > hour_ago
        ]
        
        # تحليل الخطأ واتخاذ إجراء
        self._analyze_and_respond(error_type, error_message, context)
        
        logger.error(f"[{error_type}] {error_message}")
    
    def _analyze_and_respond(self, error_type: str, error_message: str, context: Dict):
        """تحليل الخطأ واتخاذ إجراء مناسب"""
        error_count = len(self.error_counts.get(error_type, []))

        # إذا تكرر الخطأ كثيراً
        if error_count >= self.max_errors_per_hour:
            self._handle_critical_error(error_type, error_message, context)
            return

        # محاولة الإصلاح التلقائي
        if self._should_attempt_recovery(error_type):
            # التحقق من وجود event loop قبل إنشاء task
            try:
                loop = asyncio.get_running_loop()
                asyncio.create_task(self._attempt_recovery(error_type, error_message, context))
            except RuntimeError:
                # لا يوجد event loop، تشغيل الإصلاح بشكل متزامن
                logger.info(f"🔧 محاولة إصلاح متزامن لـ: {error_type}")
                self._attempt_recovery_sync(error_type, error_message, context)
    
    def _should_attempt_recovery(self, error_type: str) -> bool:
        """تحديد ما إذا كان يجب محاولة الإصلاح"""
        last_attempt = self.recovery_attempts.get(error_type, 0)
        return time.time() - last_attempt > self.recovery_cooldown

    def _attempt_recovery_sync(self, error_type: str, error_message: str, context: Dict):
        """محاولة الإصلاح التلقائي بشكل متزامن"""
        self.recovery_attempts[error_type] = time.time()

        logger.info(f"🔧 محاولة إصلاح متزامن لـ: {error_type}")

        try:
            if error_type == "tts_engine_failure":
                self._recover_tts_engine_sync(context)
            elif error_type == "telegram_bot_error":
                logger.info("✅ تم تسجيل خطأ التيليجرام للمراجعة")

            logger.info(f"✅ تم إصلاح {error_type} بنجاح")

        except Exception as e:
            logger.error(f"❌ فشل في إصلاح {error_type}: {str(e)}")

    def _recover_tts_engine_sync(self, context: Dict):
        """إصلاح محرك TTS بشكل متزامن"""
        try:
            # التبديل إلى محرك TTS آخر
            from utils import settings
            current_engine = settings.config["settings"]["tts"]["voice_choice"]

            # قائمة المحركات البديلة
            fallback_engines = ["GoogleTranslate", "pyttsx", "AWSPolly"]

            for engine in fallback_engines:
                if engine != current_engine:
                    settings.config["settings"]["tts"]["voice_choice"] = engine
                    logger.info(f"🔄 تم التبديل إلى محرك TTS: {engine}")
                    break

        except Exception as e:
            raise Exception(f"فشل في إصلاح محرك TTS: {str(e)}")
    
    async def _attempt_recovery(self, error_type: str, error_message: str, context: Dict):
        """محاولة الإصلاح التلقائي"""
        self.recovery_attempts[error_type] = time.time()
        
        logger.info(f"🔧 محاولة إصلاح تلقائي لـ: {error_type}")
        
        try:
            if error_type == "tts_engine_failure":
                await self._recover_tts_engine(context)
            elif error_type == "reddit_connection_error":
                await self._recover_reddit_connection()
            elif error_type == "screenshot_timeout":
                await self._recover_screenshot_system()
            elif error_type == "telegram_bot_error":
                await self._recover_telegram_bot()
            
            # إرسال إشعار نجاح الإصلاح
            recovery_msg = f"✅ تم إصلاح {error_type} تلقائياً"
            await send_notification(recovery_msg)
            
        except Exception as e:
            logger.error(f"❌ فشل الإصلاح التلقائي لـ {error_type}: {str(e)}")
            await self._handle_critical_error(error_type, f"فشل الإصلاح: {str(e)}", context)
    
    async def _recover_tts_engine(self, context: Dict):
        """إصلاح محرك TTS"""
        from TTS.smart_tts_manager import SmartTTSManager
        
        # إعادة تعيين المحركات الفاشلة
        tts_manager = SmartTTSManager()
        tts_manager.failed_engines.clear()
        tts_manager.retry_counts.clear()
        
        logger.info("🔄 تم إعادة تعيين محركات TTS")
    
    async def _recover_reddit_connection(self):
        """إصلاح اتصال Reddit"""
        # إعادة تهيئة اتصال Reddit
        import praw
        
        try:
            reddit = praw.Reddit(
                client_id=settings.config["reddit"]["creds"]["client_id"],
                client_secret=settings.config["reddit"]["creds"]["client_secret"],
                username=settings.config["reddit"]["creds"]["username"],
                password=settings.config["reddit"]["creds"]["password"],
                user_agent="RedditVideoMakerBot"
            )
            
            # اختبار الاتصال
            reddit.user.me()
            logger.info("✅ تم إصلاح اتصال Reddit")
            
        except Exception as e:
            raise Exception(f"فشل في إصلاح اتصال Reddit: {str(e)}")
    
    async def _recover_screenshot_system(self):
        """إصلاح نظام لقطات الشاشة"""
        # إعادة تثبيت متصفحات Playwright
        import subprocess
        import sys
        
        try:
            subprocess.run([
                sys.executable, "-m", "playwright", "install"
            ], check=True, capture_output=True)
            
            logger.info("✅ تم إصلاح نظام لقطات الشاشة")
            
        except subprocess.CalledProcessError as e:
            raise Exception(f"فشل في إصلاح نظام لقطات الشاشة: {str(e)}")
    
    async def _recover_telegram_bot(self):
        """إصلاح بوت التيليجرام"""
        # إعادة تهيئة بوت التيليجرام
        from automation.telegram_bot import TelegramBot
        
        try:
            bot = TelegramBot()
            await bot.send_message("🔄 تم إعادة تشغيل البوت")
            logger.info("✅ تم إصلاح بوت التيليجرام")
            
        except Exception as e:
            raise Exception(f"فشل في إصلاح بوت التيليجرام: {str(e)}")
    
    async def _handle_critical_error(self, error_type: str, error_message: str, context: Dict):
        """معالجة الأخطاء الحرجة"""
        critical_msg = (
            f"🚨 خطأ حرج في النظام!\n\n"
            f"النوع: {error_type}\n"
            f"الرسالة: {error_message}\n"
            f"عدد التكرارات: {len(self.error_counts.get(error_type, []))}\n"
            f"الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
            f"يرجى التدخل اليدوي لحل المشكلة."
        )
        
        await send_error(critical_msg)
        logger.critical(f"CRITICAL ERROR: {error_type} - {error_message}")
    
    def update_system_health(self, component: str, status: bool, details: str = None):
        """تحديث حالة مكونات النظام"""
        self.system_health[component] = {
            "status": status,
            "last_update": time.time(),
            "details": details
        }
        
        if not status:
            logger.warning(f"⚠️ مشكلة في {component}: {details}")
    
    def get_health_report(self) -> str:
        """تقرير صحة النظام"""
        report = "📊 تقرير صحة النظام:\n\n"
        
        for component, health in self.system_health.items():
            if isinstance(health, dict):
                status_icon = "✅" if health["status"] else "❌"
                last_update = datetime.fromtimestamp(health["last_update"]).strftime("%H:%M:%S")
                report += f"{status_icon} {component}: آخر تحديث {last_update}\n"
                if health.get("details"):
                    report += f"   التفاصيل: {health['details']}\n"
            else:
                status_icon = "✅" if health else "❌"
                report += f"{status_icon} {component}\n"
        
        # إحصائيات الأخطاء
        report += "\n📈 إحصائيات الأخطاء (آخر ساعة):\n"
        for error_type, errors in self.error_counts.items():
            if errors:
                report += f"  {error_type}: {len(errors)} مرة\n"
        
        return report
    
    async def start_monitoring(self):
        """بدء المراقبة المستمرة"""
        logger.info("🔍 بدء نظام المراقبة الذكي")
        
        while True:
            try:
                await self._perform_health_check()
                await asyncio.sleep(self.health_check_interval)
            except Exception as e:
                logger.error(f"خطأ في نظام المراقبة: {str(e)}")
                await asyncio.sleep(self.health_check_interval)
    
    async def _perform_health_check(self):
        """فحص دوري لصحة النظام"""
        # فحص محركات TTS
        await self._check_tts_engines()
        
        # فحص اتصال Reddit
        await self._check_reddit_connection()
        
        # فحص بوت التيليجرام
        await self._check_telegram_bot()
    
    async def _check_tts_engines(self):
        """فحص محركات TTS"""
        try:
            from TTS.smart_tts_manager import SmartTTSManager
            tts_manager = SmartTTSManager()
            
            # فحص كل محرك
            for engine_name in tts_manager.engines.keys():
                available = tts_manager._is_engine_available(engine_name)
                self.update_system_health(f"tts_{engine_name}", available)
                
        except Exception as e:
            self.update_system_health("tts_engines", False, str(e))
    
    async def _check_reddit_connection(self):
        """فحص اتصال Reddit"""
        try:
            import praw
            reddit = praw.Reddit(
                client_id=settings.config["reddit"]["creds"]["client_id"],
                client_secret=settings.config["reddit"]["creds"]["client_secret"],
                username=settings.config["reddit"]["creds"]["username"],
                password=settings.config["reddit"]["creds"]["password"],
                user_agent="RedditVideoMakerBot"
            )
            reddit.user.me()
            self.update_system_health("reddit_connection", True)
            
        except Exception as e:
            self.update_system_health("reddit_connection", False, str(e))
    
    async def _check_telegram_bot(self):
        """فحص بوت التيليجرام"""
        try:
            from automation.telegram_bot import TelegramBot
            bot = TelegramBot()
            # محاولة بسيطة للتحقق
            self.update_system_health("telegram_bot", True)
            
        except Exception as e:
            self.update_system_health("telegram_bot", False, str(e))

# إنشاء مثيل عام للمراقب
smart_monitor = SmartMonitor()
