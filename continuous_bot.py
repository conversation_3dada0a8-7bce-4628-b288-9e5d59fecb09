#!/usr/bin/env python3
"""
بوت مستمر لإنشاء ونشر الفيديوهات كل 10 ساعات
يعمل بشكل مستمر ولا يتوقف بعد إنشاء فيديو واحد
"""

import time
import schedule
import logging
import sys
import threading
from datetime import datetime, timedelta
from pathlib import Path

# استيراد الوحدات المطلوبة
from main import main as create_video
from utils import settings
from utils.console import print_step, print_substep, print_markdown
from utils.ffmpeg_install import ffmpeg_install
from utils.smart_config_checker import check_config_with_telegram_notifications
from automation.telegram_bot import send_notification, send_error
from automation.smart_monitor import smart_monitor

# إعداد السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/continuous_bot.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class ContinuousVideoBot:
    def __init__(self):
        """إعداد البوت المستمر"""
        self.running = False
        self.last_video_time = None
        self.video_count = 0
        self.error_count = 0
        self.max_errors = 5  # أقصى عدد أخطاء متتالية
        
        # قراءة الإعدادات
        self.schedule_hours = settings.config.get("automation", {}).get("schedule_hours", 10)
        self.auto_upload = settings.config.get("automation", {}).get("auto_upload_youtube", True)
        
        logger.info(f"🤖 تم إعداد البوت المستمر - جدولة كل {self.schedule_hours} ساعات")

    def create_and_upload_video(self):
        """إنشاء ورفع فيديو واحد"""
        try:
            logger.info("🎬 بدء إنشاء فيديو جديد...")
            send_notification(f"🎬 بدء إنشاء فيديو #{self.video_count + 1}")
            
            # إنشاء الفيديو
            video_path = create_video()
            
            if video_path:
                self.video_count += 1
                self.last_video_time = datetime.now()
                self.error_count = 0  # إعادة تعيين عداد الأخطاء
                
                logger.info(f"✅ تم إنشاء الفيديو #{self.video_count} بنجاح: {video_path}")
                send_notification(f"✅ تم إنشاء الفيديو #{self.video_count} بنجاح!")
                
                # تحديث إحصائيات النظام
                smart_monitor.update_system_health("video_creation", True, f"فيديو #{self.video_count}")
                
                return True
            else:
                raise Exception("لم يتم إنشاء الفيديو")
                
        except Exception as e:
            self.error_count += 1
            error_msg = f"خطأ في إنشاء الفيديو #{self.video_count + 1}: {str(e)}"
            logger.error(error_msg)
            send_error("خطأ في إنشاء الفيديو", error_msg)
            
            # تحديث إحصائيات النظام
            smart_monitor.log_error("video_creation_failed", str(e), {
                "video_count": self.video_count,
                "error_count": self.error_count
            })
            
            return False

    def scheduled_video_creation(self):
        """المهمة المجدولة لإنشاء الفيديوهات"""
        if not self.running:
            return
            
        logger.info("⏰ تشغيل المهمة المجدولة...")
        
        # التحقق من عدد الأخطاء المتتالية
        if self.error_count >= self.max_errors:
            error_msg = f"تم إيقاف البوت بسبب {self.error_count} أخطاء متتالية"
            logger.error(error_msg)
            send_error("إيقاف البوت", error_msg)
            self.stop()
            return
        
        # إنشاء الفيديو
        success = self.create_and_upload_video()
        
        if success:
            next_time = datetime.now() + timedelta(hours=self.schedule_hours)
            logger.info(f"📅 الفيديو التالي في: {next_time.strftime('%Y-%m-%d %H:%M:%S')}")
            send_notification(f"📅 الفيديو التالي في: {next_time.strftime('%H:%M - %d/%m')}")
        else:
            # في حالة الفشل، إعادة المحاولة بعد ساعة واحدة
            logger.warning("⚠️ سيتم إعادة المحاولة بعد ساعة واحدة")
            schedule.every(1).hours.do(self.retry_video_creation).tag('retry')

    def retry_video_creation(self):
        """إعادة محاولة إنشاء الفيديو"""
        logger.info("🔄 إعادة محاولة إنشاء الفيديو...")
        
        # إلغاء مهمة إعادة المحاولة
        schedule.clear('retry')
        
        # محاولة إنشاء الفيديو
        self.scheduled_video_creation()

    def start(self):
        """بدء البوت المستمر"""
        logger.info("🚀 بدء البوت المستمر...")
        self.running = True
        
        # إرسال إشعار البدء
        send_notification(f"""
🚀 **تم بدء البوت المستمر**

⚙️ **الإعدادات:**
• الجدولة: كل {self.schedule_hours} ساعات
• الرفع التلقائي: {'مفعل' if self.auto_upload else 'معطل'}
• بدء التشغيل: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🎬 سيتم إنشاء الفيديو الأول خلال دقائق...
        """)
        
        # جدولة المهام
        schedule.every(self.schedule_hours).hours.do(self.scheduled_video_creation).tag('main')
        
        # إنشاء أول فيديو فوراً
        threading.Thread(target=self.create_and_upload_video, daemon=True).start()
        
        # حلقة التشغيل الرئيسية
        try:
            while self.running:
                schedule.run_pending()
                time.sleep(60)  # فحص كل دقيقة
                
        except KeyboardInterrupt:
            logger.info("⏹️ تم إيقاف البوت بواسطة المستخدم")
            self.stop()
        except Exception as e:
            logger.error(f"❌ خطأ في حلقة التشغيل: {e}")
            send_error("خطأ في البوت المستمر", str(e))
            self.stop()

    def stop(self):
        """إيقاف البوت المستمر"""
        logger.info("🛑 إيقاف البوت المستمر...")
        self.running = False
        schedule.clear()
        
        # إرسال تقرير نهائي
        runtime = datetime.now() - (self.last_video_time or datetime.now())
        send_notification(f"""
🛑 **تم إيقاف البوت المستمر**

📊 **الإحصائيات:**
• عدد الفيديوهات: {self.video_count}
• عدد الأخطاء: {self.error_count}
• وقت التشغيل: {runtime}
• آخر فيديو: {self.last_video_time.strftime('%Y-%m-%d %H:%M:%S') if self.last_video_time else 'لا يوجد'}
        """)

    def get_status(self):
        """الحصول على حالة البوت"""
        if not self.running:
            return "متوقف"
        
        next_run = None
        for job in schedule.jobs:
            if 'main' in job.tags:
                next_run = job.next_run
                break
        
        status = {
            'running': self.running,
            'video_count': self.video_count,
            'error_count': self.error_count,
            'last_video_time': self.last_video_time,
            'next_run': next_run,
            'schedule_hours': self.schedule_hours
        }
        
        return status

def setup_system():
    """إعداد النظام قبل البدء"""
    print_markdown("# 🤖 إعداد البوت المستمر")
    
    # التحقق من Python version
    if sys.version_info.major != 3 or sys.version_info.minor < 10:
        print("❌ يتطلب Python 3.10 أو أحدث")
        sys.exit(1)
    
    # تثبيت FFmpeg
    print_step("Installing FFmpeg...")
    ffmpeg_install()
    
    # فحص ملف الإعدادات
    print_step("Checking configuration...")
    directory = Path().absolute()
    config = settings.check_toml(
        f"{directory}/utils/.config.template.toml", 
        f"{directory}/config.toml"
    )
    
    if not config:
        print("❌ فشل في تحميل الإعدادات")
        sys.exit(1)
    
    # فحص الإعدادات مع النظام الذكي
    print_step("Validating configuration with smart system...")
    config_valid = check_config_with_telegram_notifications()
    
    if not config_valid:
        print_substep("⚠️ تم اكتشاف مشاكل في الإعدادات! تحقق من Telegram للحصول على التفاصيل", "bold yellow")
        print_substep("🤖 النظام الذكي سيحاول الإصلاح التلقائي", "bold blue")
    
    print_step("✅ تم إعداد النظام بنجاح")
    return True

def main():
    """الدالة الرئيسية"""
    try:
        # إعداد النظام
        if not setup_system():
            sys.exit(1)
        
        # إنشاء وبدء البوت المستمر
        bot = ContinuousVideoBot()
        bot.start()
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف البرنامج بواسطة المستخدم")
    except Exception as e:
        logger.error(f"❌ خطأ حرج: {e}")
        send_error("خطأ حرج في البوت المستمر", str(e))
        sys.exit(1)

if __name__ == "__main__":
    main()
