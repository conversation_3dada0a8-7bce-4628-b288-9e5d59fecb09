#!/usr/bin/env python3
"""
نظام تشغيل مولد الفيديوهات التلقائي المتكامل
يدمج جميع الوحدات: الجدولة، YouTube، Telegram، Gemini AI، واجهة المراقبة
"""

import sys
import logging
import threading
import time
import signal
from pathlib import Path

# إعداد نظام السجلات
Path("logs").mkdir(exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/system.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def check_requirements():
    """فحص المتطلبات الأساسية"""
    logger.info("🔍 فحص المتطلبات الأساسية...")
    
    # فحص ملفات الإعدادات
    required_files = [
        'config.toml',
        'service_account.json'  # ملف Google Service Account
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        logger.error(f"❌ ملفات مطلوبة مفقودة: {', '.join(missing_files)}")
        logger.error("يرجى التأكد من وجود:")
        logger.error("1. config.toml - ملف إعدادات Reddit")
        logger.error("2. service_account.json - ملف مصادقة Google")
        return False
    
    # فحص المجلدات المطلوبة
    required_dirs = ['logs', 'results', 'assets']
    for dir_name in required_dirs:
        Path(dir_name).mkdir(exist_ok=True)
    
    logger.info("✅ جميع المتطلبات متوفرة")
    return True

def setup_signal_handlers():
    """إعداد معالجات الإشارات للإغلاق الآمن"""
    def signal_handler(signum, frame):
        logger.info(f"تم استلام إشارة الإغلاق: {signum}")
        logger.info("🛑 إيقاف النظام بأمان...")
        
        # إرسال إشعار إيقاف
        try:
            from automation.telegram_bot import send_notification
            send_notification("🛑 تم إيقاف نظام إنشاء الفيديوهات بواسطة المستخدم")
        except:
            pass
            
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

def start_dashboard():
    """بدء واجهة المراقبة في خيط منفصل"""
    try:
        logger.info("🌐 بدء تشغيل واجهة المراقبة...")
        from automation.dashboard import start_dashboard_thread
        dashboard_thread = start_dashboard_thread()
        logger.info("✅ تم تشغيل واجهة المراقبة على http://localhost:5000")
        return dashboard_thread
    except Exception as e:
        logger.error(f"❌ فشل في تشغيل واجهة المراقبة: {e}")
        return None

def start_scheduler():
    """بدء نظام الجدولة"""
    try:
        logger.info("📅 بدء تشغيل نظام الجدولة...")
        from scheduler import VideoScheduler
        
        scheduler = VideoScheduler()
        scheduler.setup_schedule()
        
        # تشغيل في خيط منفصل
        scheduler_thread = threading.Thread(
            target=scheduler.run_scheduler, 
            daemon=True,
            name="VideoScheduler"
        )
        scheduler_thread.start()
        
        logger.info("✅ تم تشغيل نظام الجدولة")
        return scheduler_thread
    except Exception as e:
        logger.error(f"❌ فشل في تشغيل نظام الجدولة: {e}")
        return None

def test_integrations():
    """اختبار التكاملات الأساسية"""
    logger.info("🧪 اختبار التكاملات...")
    
    # اختبار Telegram Bot
    try:
        from automation.telegram_bot import send_notification
        send_notification("🧪 اختبار بوت Telegram - النظام يعمل!")
        logger.info("✅ Telegram Bot يعمل")
    except Exception as e:
        logger.warning(f"⚠️ مشكلة في Telegram Bot: {e}")
    
    # اختبار Gemini AI
    try:
        from automation.gemini_content_generator import GeminiContentGenerator
        generator = GeminiContentGenerator()
        test_result = generator.generate_content(
            "Test Reddit Post", 
            "This is a test content for AI generation",
            "AskReddit"
        )
        if test_result and test_result.get('title'):
            logger.info("✅ Gemini AI يعمل")
        else:
            logger.warning("⚠️ مشكلة في Gemini AI - استجابة غير صحيحة")
    except Exception as e:
        logger.warning(f"⚠️ مشكلة في Gemini AI: {e}")
    
    # اختبار YouTube API (بدون رفع فعلي)
    try:
        from automation.youtube_uploader import YouTubeUploader
        uploader = YouTubeUploader()
        channel_info = uploader.get_channel_info()
        if channel_info:
            logger.info(f"✅ YouTube API يعمل - القناة: {channel_info.get('title', 'غير معروف')}")
        else:
            logger.warning("⚠️ مشكلة في YouTube API - لا يمكن الحصول على معلومات القناة")
    except Exception as e:
        logger.warning(f"⚠️ مشكلة في YouTube API: {e}")

def display_system_info():
    """عرض معلومات النظام"""
    logger.info("=" * 60)
    logger.info("🤖 نظام إنشاء الفيديوهات التلقائي")
    logger.info("=" * 60)
    logger.info("📋 المكونات:")
    logger.info("  • مولد المحتوى: Gemini 2.5 Pro")
    logger.info("  • منصة النشر: YouTube Shorts")
    logger.info("  • الإشعارات: Telegram Bot")
    logger.info("  • المراقبة: واجهة ويب")
    logger.info("  • الجدولة: كل 10 ساعات")
    logger.info("=" * 60)
    logger.info("🔗 الروابط:")
    logger.info("  • واجهة المراقبة: http://localhost:5000")
    logger.info("  • السجلات: logs/")
    logger.info("  • النتائج: results/")
    logger.info("=" * 60)

def main():
    """الدالة الرئيسية"""
    try:
        # عرض معلومات النظام
        display_system_info()
        
        # إعداد معالجات الإشارات
        setup_signal_handlers()
        
        # فحص المتطلبات
        if not check_requirements():
            logger.error("❌ فشل في فحص المتطلبات")
            sys.exit(1)
        
        # اختبار التكاملات
        test_integrations()
        
        # بدء واجهة المراقبة
        dashboard_thread = start_dashboard()
        
        # بدء نظام الجدولة
        scheduler_thread = start_scheduler()
        
        if not scheduler_thread:
            logger.error("❌ فشل في بدء النظام")
            sys.exit(1)
        
        # إرسال إشعار بدء التشغيل
        try:
            from automation.telegram_bot import send_notification
            send_notification("""
🚀 تم بدء تشغيل نظام إنشاء الفيديوهات التلقائي!

📊 المعلومات:
• الجدولة: كل 10 ساعات (8 صباحاً و 8 مساءً)
• المنصة: YouTube Shorts
• الذكاء الاصطناعي: Gemini 2.5 Pro
• واجهة المراقبة: http://localhost:5000

✅ النظام جاهز للعمل!
            """.strip())
        except Exception as e:
            logger.warning(f"فشل في إرسال إشعار البدء: {e}")
        
        logger.info("🎉 تم تشغيل النظام بنجاح!")
        logger.info("💡 لإيقاف النظام، اضغط Ctrl+C")
        
        # حلقة رئيسية للحفاظ على البرنامج
        try:
            while True:
                time.sleep(60)  # فحص كل دقيقة
                
                # فحص حالة الخيوط
                if scheduler_thread and not scheduler_thread.is_alive():
                    logger.error("❌ توقف نظام الجدولة، إعادة تشغيل...")
                    scheduler_thread = start_scheduler()
                    
                if dashboard_thread and not dashboard_thread.is_alive():
                    logger.warning("⚠️ توقفت واجهة المراقبة، إعادة تشغيل...")
                    dashboard_thread = start_dashboard()
                    
        except KeyboardInterrupt:
            logger.info("تم إيقاف النظام بواسطة المستخدم")
        
    except Exception as e:
        logger.error(f"❌ خطأ في النظام الرئيسي: {e}")
        
        # إرسال إشعار خطأ حرج
        try:
            from automation.telegram_bot import send_error
            send_error("خطأ حرج في النظام الرئيسي", str(e))
        except:
            pass
            
        sys.exit(1)

if __name__ == "__main__":
    main()
