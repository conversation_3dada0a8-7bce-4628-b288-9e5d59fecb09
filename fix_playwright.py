#!/usr/bin/env python3
"""
أداة إصلاح مشاكل Playwright
"""

import subprocess
import sys
import os
from pathlib import Path

def install_playwright():
    """تثبيت Playwright والمتصفحات"""
    print("🔄 تثبيت Playwright...")
    
    try:
        # تثبيت playwright
        subprocess.run([sys.executable, "-m", "pip", "install", "playwright"], check=True)
        print("✅ تم تثبيت Playwright")
        
        # تثبيت المتصفحات
        print("🔄 تثبيت متصفحات Playwright...")
        subprocess.run([sys.executable, "-m", "playwright", "install"], check=True)
        print("✅ تم تثبيت المتصفحات")
        
        # تثبيت dependencies للنظام
        print("🔄 تثبيت dependencies النظام...")
        subprocess.run([sys.executable, "-m", "playwright", "install-deps"], check=True)
        print("✅ تم تثبيت dependencies النظام")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ خطأ في التثبيت: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        return False

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    directories = [
        "assets/temp",
        "assets/backgrounds", 
        "logs",
        "video_creation/data"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ تم إنشاء مجلد: {directory}")

def check_playwright():
    """فحص تثبيت Playwright"""
    try:
        import playwright
        print(f"✅ Playwright مثبت - الإصدار: {playwright.__version__}")
        
        # فحص المتصفحات
        result = subprocess.run([sys.executable, "-m", "playwright", "--help"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Playwright CLI يعمل بشكل صحيح")
            return True
        else:
            print("❌ مشكلة في Playwright CLI")
            return False
            
    except ImportError:
        print("❌ Playwright غير مثبت")
        return False
    except Exception as e:
        print(f"❌ خطأ في فحص Playwright: {e}")
        return False

def fix_common_issues():
    """إصلاح المشاكل الشائعة"""
    print("🔧 إصلاح المشاكل الشائعة...")
    
    # إنشاء المجلدات
    create_directories()
    
    # فحص ملفات cookies
    cookie_files = [
        "video_creation/data/cookie-dark-mode.json",
        "video_creation/data/cookie-light-mode.json"
    ]
    
    for cookie_file in cookie_files:
        if not Path(cookie_file).exists():
            print(f"⚠️ ملف cookies مفقود: {cookie_file}")
            # إنشاء ملف cookies بسيط
            cookie_data = [
                {
                    "name": "reddit_session",
                    "value": "",
                    "domain": ".reddit.com",
                    "path": "/"
                }
            ]
            
            Path(cookie_file).parent.mkdir(parents=True, exist_ok=True)
            with open(cookie_file, 'w') as f:
                import json
                json.dump(cookie_data, f, indent=2)
            print(f"✅ تم إنشاء ملف cookies: {cookie_file}")

def test_playwright():
    """اختبار Playwright"""
    print("🧪 اختبار Playwright...")
    
    try:
        from playwright.sync_api import sync_playwright
        
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            page = browser.new_page()
            page.goto("https://www.google.com")
            title = page.title()
            browser.close()
            
            if "Google" in title:
                print("✅ Playwright يعمل بشكل صحيح")
                return True
            else:
                print("❌ مشكلة في تشغيل Playwright")
                return False
                
    except Exception as e:
        print(f"❌ خطأ في اختبار Playwright: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("""
🔧 أداة إصلاح مشاكل Playwright
==============================

هذه الأداة ستساعد في حل مشاكل التقاط لقطات الشاشة.
    """)
    
    # فحص التثبيت الحالي
    if not check_playwright():
        print("\n🔄 إعادة تثبيت Playwright...")
        if not install_playwright():
            print("❌ فشل في تثبيت Playwright")
            return
    
    # إصلاح المشاكل الشائعة
    fix_common_issues()
    
    # اختبار التشغيل
    if test_playwright():
        print("""
✅ تم إصلاح Playwright بنجاح!

يمكنك الآن تشغيل البوت:
python main.py
        """)
    else:
        print("""
❌ لا تزال هناك مشاكل في Playwright

جرب:
1. إعادة تشغيل الكمبيوتر
2. تشغيل الأمر كمدير (Run as Administrator)
3. التحقق من اتصال الإنترنت
        """)

if __name__ == "__main__":
    main()
