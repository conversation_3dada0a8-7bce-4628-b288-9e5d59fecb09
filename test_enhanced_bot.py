#!/usr/bin/env python3
"""
اختبار شامل للبوت المحسن
يختبر جميع الوظائف والمميزات الجديدة
"""

import asyncio
import logging
import sys
import time
from pathlib import Path

# إعداد السجلات
Path("logs").mkdir(exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/test_bot.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def test_message_router():
    """اختبار نظام توجيه الرسائل"""
    print("🧪 اختبار نظام توجيه الرسائل...")
    
    try:
        from automation.message_router import (
            send_telegram_message, 
            send_telegram_error, 
            send_telegram_success, 
            send_telegram_warning,
            send_telegram_progress
        )
        
        # اختبار الرسائل المختلفة
        send_telegram_message("🧪 اختبار رسالة عادية", "INFO")
        time.sleep(1)
        
        send_telegram_success("اختبار رسالة نجاح", "تم الاختبار بنجاح")
        time.sleep(1)
        
        send_telegram_warning("اختبار رسالة تحذير", "هذا مجرد اختبار")
        time.sleep(1)
        
        send_telegram_error("اختبار رسالة خطأ", "هذا خطأ تجريبي", {"test": "value"})
        time.sleep(1)
        
        send_telegram_progress("اختبار التقدم", "50%", 10, 5)
        time.sleep(1)
        
        print("✅ نظام توجيه الرسائل يعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في نظام توجيه الرسائل: {e}")
        return False

def test_telegram_bot():
    """اختبار البوت الأساسي"""
    print("🧪 اختبار البوت الأساسي...")

    try:
        from automation.telegram_bot import EnhancedTelegramBot

        # فحص معرف المحادثة أولاً
        chat_id_file = "telegram_chat_id.txt"
        if Path(chat_id_file).exists():
            with open(chat_id_file, 'r') as f:
                chat_id = f.read().strip()
                if chat_id == "YOUR_CHAT_ID_HERE" or not chat_id:
                    print("⚠️ معرف المحادثة غير محدد. يرجى:")
                    print("1. بدء محادثة مع البوت في التلغرام")
                    print("2. إرسال /start")
                    print("3. إعادة تشغيل الاختبار")
                    return False

        # إنشاء البوت
        bot = EnhancedTelegramBot()

        # اختبار إرسال رسالة
        success = bot.send_message("🧪 اختبار البوت المحسن")

        if success:
            print("✅ البوت الأساسي يعمل بشكل صحيح")
            return True
        else:
            print("⚠️ البوت الأساسي لا يرسل الرسائل (قد يكون معرف المحادثة غير صحيح)")
            return False

    except Exception as e:
        print(f"❌ خطأ في البوت الأساسي: {e}")
        return False

def test_config_loading():
    """اختبار تحميل الإعدادات"""
    print("🧪 اختبار تحميل الإعدادات...")

    try:
        import toml

        # قراءة ملف الإعدادات مباشرة
        with open("config.toml", "r", encoding="utf-8") as f:
            config = toml.load(f)

        # فحص الإعدادات المطلوبة
        required_configs = [
            ("telegram", "bot_token"),
            ("reddit", "creds", "client_id"),
            ("gemini", "api_key")
        ]

        missing_configs = []
        for config_path in required_configs:
            current = config

            try:
                for key in config_path:
                    current = current[key]
                if not current or str(current).strip() == "":
                    missing_configs.append(".".join(config_path))
            except KeyError:
                missing_configs.append(".".join(config_path))

        if missing_configs:
            print(f"⚠️ إعدادات مفقودة: {', '.join(missing_configs)}")
            return False
        else:
            print("✅ جميع الإعدادات متوفرة")
            return True

    except Exception as e:
        print(f"❌ خطأ في تحميل الإعدادات: {e}")
        return False

def test_file_structure():
    """اختبار هيكل الملفات"""
    print("🧪 اختبار هيكل الملفات...")
    
    required_files = [
        "config.toml",
        "automation/telegram_bot.py",
        "automation/message_router.py",
        "start_enhanced_system.py",
        "start_enhanced_telegram_bot.py"
    ]
    
    required_dirs = [
        "logs",
        "automation",
        "utils"
    ]
    
    missing_files = []
    missing_dirs = []
    
    # فحص الملفات
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    # فحص المجلدات
    for dir_path in required_dirs:
        if not Path(dir_path).exists():
            missing_dirs.append(dir_path)
    
    if missing_files or missing_dirs:
        if missing_files:
            print(f"❌ ملفات مفقودة: {', '.join(missing_files)}")
        if missing_dirs:
            print(f"❌ مجلدات مفقودة: {', '.join(missing_dirs)}")
        return False
    else:
        print("✅ هيكل الملفات صحيح")
        return True

async def test_interactive_bot():
    """اختبار البوت التفاعلي (لمدة قصيرة)"""
    print("🧪 اختبار البوت التفاعلي...")
    
    try:
        from automation.telegram_bot import EnhancedTelegramBot
        
        bot = EnhancedTelegramBot()
        
        # إرسال رسالة اختبار
        bot.send_message("🧪 **اختبار البوت التفاعلي**\n\nالبوت جاهز للاختبار!\nأرسل /start لاختبار الأزرار")
        
        print("✅ البوت التفاعلي جاهز")
        print("💡 يمكنك الآن اختبار الأزرار في التلغرام")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في البوت التفاعلي: {e}")
        return False

def run_all_tests():
    """تشغيل جميع الاختبارات"""
    print("""
    🧪 اختبار شامل للنظام المحسن
    ===============================
    
    سيتم اختبار جميع المكونات الجديدة:
    • نظام توجيه الرسائل
    • البوت الأساسي
    • تحميل الإعدادات
    • هيكل الملفات
    • البوت التفاعلي
    
    """)
    
    tests = [
        ("هيكل الملفات", test_file_structure),
        ("تحميل الإعدادات", test_config_loading),
        ("البوت الأساسي", test_telegram_bot),
        ("نظام توجيه الرسائل", test_message_router),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"🧪 اختبار: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            results[test_name] = result
            
            if result:
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
                
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
            results[test_name] = False
        
        time.sleep(2)  # انتظار بين الاختبارات
    
    # عرض النتائج النهائية
    print(f"\n{'='*50}")
    print("📊 نتائج الاختبارات")
    print('='*50)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ نجح" if result else "❌ فشل"
        print(f"• {test_name}: {status}")
    
    print(f"\n📈 النتيجة النهائية: {passed}/{total} ({(passed/total)*100:.1f}%)")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت، يرجى مراجعة الأخطاء")
        return False

async def main():
    """الدالة الرئيسية"""
    try:
        # تشغيل الاختبارات الأساسية
        basic_tests_passed = run_all_tests()
        
        if basic_tests_passed:
            print(f"\n{'='*50}")
            print("🧪 اختبار البوت التفاعلي")
            print('='*50)
            
            # اختبار البوت التفاعلي
            interactive_test = await test_interactive_bot()
            
            if interactive_test:
                print("\n🎉 جميع الاختبارات نجحت!")
                print("💡 يمكنك الآن استخدام النظام بثقة")
                print("🚀 لتشغيل النظام: python start_enhanced_system.py")
            else:
                print("\n⚠️ البوت التفاعلي لا يعمل بشكل صحيح")
        else:
            print("\n❌ فشلت الاختبارات الأساسية")
            print("🔧 يرجى إصلاح المشاكل قبل المتابعة")
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار الرئيسي: {e}")

if __name__ == "__main__":
    asyncio.run(main())
