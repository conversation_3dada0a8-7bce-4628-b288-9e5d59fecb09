name: 'Stale issue handler'
on:
  workflow_dispatch:
  schedule:
    - cron: '0 0 * * *'

jobs:

  stale:
    runs-on: ubuntu-latest
    permissions:
      issues: write
      pull-requests: write
    steps:
      - uses: actions/stale@v9
        id: stale-issue
        name: stale-issue
        with:
          # general settings
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          stale-issue-message: 'This issue is stale because it has been open 7 days with no activity. Remove stale label or comment, or this will be closed in 10 days.'
          close-issue-message: 'Issue closed due to being stale. Please reopen if issue persists in latest version.'
          days-before-stale: 7
          days-before-close: 15
          stale-issue-label: 'stale'
          close-issue-label: 'outdated'
          exempt-issue-labels: 'enhancement,keep,blocked'
          exempt-all-issue-milestones: true
          operations-per-run: 300
          remove-stale-when-updated: true
          ascending: true
          #debug-only: true

          stale-pr-message: 'This pull request is stale as it has been open for 7 days with no activity. Remove stale label or comment, or this will be closed in 10 days.'
          close-pr-message: 'Pull request closed due to being stale.'
          close-pr-label: 'outdated'
          stale-pr-label: 'stale'
          exempt-pr-labels: 'keep,blocked,before next release,after next release'
  
