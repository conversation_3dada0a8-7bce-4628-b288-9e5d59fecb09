# 🆓 دليل النشر على Google Cloud Free Tier

## 🎯 الهدف: استخدام Always Free Tier فقط (بدون استهلاك الـ 300$)

هذا الدليل يوضح كيفية نشر نظام إنشاء الفيديوهات على Google Cloud باستخدام **الموارد المجانية الدائمة فقط** دون المساس بالـ 300 دولار المجانية.

---

## 📋 الموارد المستخدمة (Always Free)

### ✅ **App Engine (F1 Instance)**
- **28 ساعة يومياً مجانية**
- واجهة التحكم والتنسيق
- لا يستهلك من الـ 300$

### ✅ **Cloud Functions**
- **2 مليون استدعاء شهرياً**
- **400,000 GB-ثانية معالجة**
- معالجة الفيديوهات
- لا يستهلك من الـ 300$

### ✅ **Cloud Storage**
- **5 GB تخزين مجاني**
- **100 GB نقل صادر**
- تخزين الملفات المؤقتة
- لا يستهلك من الـ 300$

### ✅ **Cloud Scheduler**
- **3 مهام مجانية**
- جدولة إنشاء الفيديوهات
- لا يستهلك من الـ 300$

### ✅ **Cloud Build**
- **120 دقيقة بناء يومياً**
- بناء ونشر التطبيق
- لا يستهلك من الـ 300$

---

## 🚀 خطوات النشر

### **1. إعداد Google Cloud Project**

```bash
# تثبيت gcloud CLI
# من: https://cloud.google.com/sdk

# تسجيل الدخول
gcloud auth login

# إنشاء مشروع جديد
gcloud projects create reddit-video-maker-free --name="Reddit Video Maker Free"

# تعيين المشروع
gcloud config set project reddit-video-maker-free

# تفعيل الفوترة (مطلوب حتى للموارد المجانية)
# اذهب إلى: https://console.cloud.google.com/billing
# وربط المشروع بحساب فوترة (لن يتم خصم أي مبلغ)
```

### **2. تفعيل الخدمات المجانية**

```bash
# تفعيل الخدمات المطلوبة (كلها مجانية)
gcloud services enable appengine.googleapis.com
gcloud services enable cloudfunctions.googleapis.com
gcloud services enable cloudscheduler.googleapis.com
gcloud services enable storage.googleapis.com
gcloud services enable cloudbuild.googleapis.com
gcloud services enable youtube.googleapis.com
```

### **3. إنشاء App Engine**

```bash
# إنشاء تطبيق App Engine في منطقة مجانية
gcloud app create --region=us-central
```

### **4. إعداد Cloud Storage**

```bash
# إنشاء buckets في منطقة مجانية
gsutil mb -l us-central1 gs://reddit-video-maker-free-assets
gsutil mb -l us-central1 gs://reddit-video-maker-free-results

# تطبيق سياسة حذف تلقائي لتوفير المساحة
gsutil lifecycle set cloud_deployment/storage_lifecycle.json gs://reddit-video-maker-free-assets
gsutil lifecycle set cloud_deployment/storage_lifecycle.json gs://reddit-video-maker-free-results
```

### **5. إعداد ملفات الإعدادات**

#### **أ. إعداد Reddit API**
```bash
# تحديث cloud_deployment/app.yaml
# أضف بيانات Reddit API:
env_variables:
  REDDIT_CLIENT_ID: "your_reddit_client_id"
  REDDIT_CLIENT_SECRET: "your_reddit_client_secret"
  REDDIT_USERNAME: "your_reddit_username"
  REDDIT_PASSWORD: "your_reddit_password"
```

#### **ب. إعداد YouTube API**
```bash
# ضع ملف service_account.json في cloud_deployment/
cp service_account.json cloud_deployment/
```

#### **ج. إعداد Telegram Bot**
```bash
# تحديث متغيرات البيئة في app.yaml
env_variables:
  TELEGRAM_BOT_TOKEN: "**********************************************"
```

### **6. نشر Cloud Functions**

```bash
# نشر دالة معالجة الفيديو
gcloud functions deploy video-processor \
    --runtime python39 \
    --trigger-http \
    --memory 256MB \
    --timeout 540s \
    --max-instances 1 \
    --source cloud_deployment/functions \
    --entry-point process_video \
    --allow-unauthenticated \
    --region us-central1
```

### **7. نشر App Engine**

```bash
# نشر التطبيق الرئيسي
gcloud app deploy cloud_deployment/app.yaml --quiet
```

### **8. إعداد Cloud Scheduler**

```bash
# إنشاء مهمة الفيديو الصباحي
gcloud scheduler jobs create http morning-video-job \
    --schedule="0 8 * * *" \
    --uri="https://reddit-video-maker-free.appspot.com/create-video" \
    --http-method=POST \
    --headers="Content-Type=application/json" \
    --message-body='{"type":"morning"}' \
    --time-zone="Asia/Riyadh"

# إنشاء مهمة الفيديو المسائي
gcloud scheduler jobs create http evening-video-job \
    --schedule="0 20 * * *" \
    --uri="https://reddit-video-maker-free.appspot.com/create-video" \
    --http-method=POST \
    --headers="Content-Type=application/json" \
    --message-body='{"type":"evening"}' \
    --time-zone="Asia/Riyadh"

# إنشاء مهمة تنظيف يومية
gcloud scheduler jobs create http cleanup-job \
    --schedule="0 2 * * *" \
    --uri="https://reddit-video-maker-free.appspot.com/cleanup" \
    --http-method=POST \
    --time-zone="Asia/Riyadh"
```

---

## 📊 مراقبة الاستخدام

### **1. لوحة التحكم**
- **الرابط**: `https://reddit-video-maker-free.appspot.com`
- **المراقبة**: استخدام الموارد المجانية
- **التحكم**: إنشاء فيديوهات يدوياً

### **2. Google Cloud Console**
- **App Engine**: https://console.cloud.google.com/appengine
- **Cloud Functions**: https://console.cloud.google.com/functions
- **Cloud Storage**: https://console.cloud.google.com/storage
- **Cloud Scheduler**: https://console.cloud.google.com/cloudscheduler

### **3. مراقبة الحدود**
```bash
# فحص استخدام App Engine
gcloud app instances list

# فحص استخدام Cloud Functions
gcloud functions describe video-processor --region=us-central1

# فحص استخدام Cloud Storage
gsutil du -sh gs://reddit-video-maker-free-*
```

---

## ⚠️ تحذيرات مهمة

### **🚫 تجنب هذه الخدمات (تستهلك من الـ 300$)**
- ❌ **Compute Engine** (إلا e2-micro في المناطق المحددة)
- ❌ **Cloud SQL** (ليس له طبقة مجانية دائمة)
- ❌ **Cloud Run** (له حدود مجانية لكن محدودة)
- ❌ **Kubernetes Engine** (إلا cluster واحد مجاني)

### **✅ التأكد من البقاء في الحدود المجانية**
- **App Engine**: لا تتجاوز 28 ساعة F1 يومياً
- **Cloud Functions**: لا تتجاوز 2M استدعاء شهرياً
- **Cloud Storage**: لا تتجاوز 5 GB
- **مراقبة مستمرة**: عبر لوحة التحكم

---

## 🔧 تحسينات الأداء

### **1. تحسين استهلاك الذاكرة**
```python
# في Cloud Functions
FREE_TIER_CONFIG = {
    'max_memory_mb': 256,  # الحد الأدنى
    'cleanup_immediately': True,
    'compress_assets': True
}
```

### **2. تحسين التخزين**
```json
// في storage_lifecycle.json
{
  "rule": [
    {
      "action": {"type": "Delete"},
      "condition": {"age": 1}  // حذف بعد يوم واحد
    }
  ]
}
```

### **3. تحسين الجدولة**
- فيديوان يومياً فقط (صباح ومساء)
- تنظيف تلقائي يومي
- مراقبة الحدود

---

## 📈 الإحصائيات المتوقعة

### **الاستخدام الشهري المتوقع:**
- **App Engine**: ~20 ساعة يومياً (من أصل 28)
- **Cloud Functions**: ~1000 استدعاء شهرياً (من أصل 2M)
- **Cloud Storage**: ~2 GB (من أصل 5 GB)
- **التكلفة**: **0.00$ - مجاني تماماً!**

### **الإنتاج المتوقع:**
- **60 فيديو شهرياً** (2 يومياً)
- **جودة متوسطة** (محسنة للموارد المحدودة)
- **نشر تلقائي** على YouTube
- **إشعارات Telegram**

---

## 🆘 استكشاف الأخطاء

### **مشكلة: "تجاوز حد الذاكرة"**
```bash
# تقليل حجم الفيديو
# تحسين الكود
# تنظيف الملفات المؤقتة
```

### **مشكلة: "تجاوز حد التخزين"**
```bash
# تشغيل التنظيف اليدوي
curl -X POST https://reddit-video-maker-free.appspot.com/cleanup
```

### **مشكلة: "تجاوز حد الاستدعاءات"**
```bash
# تقليل تكرار الجدولة
# تحسين الكود لتقليل الاستدعاءات
```

---

## ✅ قائمة التحقق النهائية

- [ ] تم إنشاء المشروع وربطه بحساب فوترة
- [ ] تم تفعيل جميع الخدمات المطلوبة
- [ ] تم نشر App Engine بنجاح
- [ ] تم نشر Cloud Functions بنجاح
- [ ] تم إعداد Cloud Storage مع سياسة الحذف
- [ ] تم إعداد Cloud Scheduler للمهام
- [ ] تم اختبار إنشاء فيديو واحد
- [ ] تعمل لوحة التحكم بشكل صحيح
- [ ] تصل إشعارات Telegram
- [ ] الاستخدام ضمن الحدود المجانية

**🎉 مبروك! نظامك يعمل مجاناً تماماً على Google Cloud!**

---

## 📞 الدعم

إذا واجهت مشاكل:
1. تحقق من لوحة التحكم: `https://your-project.appspot.com`
2. راجع سجلات Cloud Functions: `gcloud functions logs read video-processor`
3. تحقق من حالة App Engine: `gcloud app logs tail -s default`
4. مراقبة الاستخدام: Google Cloud Console

**💡 نصيحة**: احتفظ بمراقبة مستمرة للاستخدام لضمان البقاء ضمن الحدود المجانية!
