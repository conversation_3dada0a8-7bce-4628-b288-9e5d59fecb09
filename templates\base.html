<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}لوحة تحكم إنشاء الفيديوهات{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        body {
            background-color: #f8f9fa;
            font-family: '<PERSON><PERSON><PERSON>', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar-brand {
            font-weight: bold;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s;
        }
        
        .card:hover {
            transform: translateY(-2px);
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .stat-card.success {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .stat-card.warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        .stat-card.info {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-left: 8px;
        }
        
        .status-running {
            background-color: #28a745;
            animation: pulse 2s infinite;
        }
        
        .status-stopped {
            background-color: #dc3545;
        }
        
        .status-error {
            background-color: #ffc107;
            animation: blink 1s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }
        
        .log-entry {
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            padding: 8px;
            margin: 2px 0;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        
        .log-entry.error {
            background-color: #f8d7da;
            border-left-color: #dc3545;
        }
        
        .log-entry.warning {
            background-color: #fff3cd;
            border-left-color: #ffc107;
        }
        
        .log-entry.info {
            background-color: #d1ecf1;
            border-left-color: #17a2b8;
        }
        
        .sidebar {
            background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            transition: all 0.3s;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
        }
        
        .refresh-btn {
            position: fixed;
            bottom: 20px;
            left: 20px;
            z-index: 1000;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="bi bi-camera-video"></i>
                مولد الفيديوهات التلقائي
            </a>
            
            <div class="navbar-nav ms-auto">
                <span class="navbar-text">
                    <span id="system-status">
                        <span class="status-indicator status-running"></span>
                        النظام يعمل
                    </span>
                </span>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 sidebar p-3">
                <ul class="nav nav-pills flex-column">
                    <li class="nav-item mb-2">
                        <a class="nav-link {% if request.endpoint == 'dashboard' %}active{% endif %}" 
                           href="{{ url_for('dashboard') }}">
                            <i class="bi bi-speedometer2"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item mb-2">
                        <a class="nav-link {% if request.endpoint == 'logs_page' %}active{% endif %}" 
                           href="{{ url_for('logs_page') }}">
                            <i class="bi bi-file-text"></i>
                            السجلات
                        </a>
                    </li>
                    <li class="nav-item mb-2">
                        <a class="nav-link {% if request.endpoint == 'settings_page' %}active{% endif %}" 
                           href="{{ url_for('settings_page') }}">
                            <i class="bi bi-gear"></i>
                            الإعدادات
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-10 p-4">
                {% block content %}{% endblock %}
            </div>
        </div>
    </div>

    <!-- Refresh Button -->
    <button class="btn btn-primary refresh-btn" onclick="location.reload()">
        <i class="bi bi-arrow-clockwise"></i>
    </button>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Auto-refresh script -->
    <script>
        // تحديث تلقائي كل 30 ثانية
        setInterval(function() {
            updateSystemStatus();
        }, 30000);
        
        function updateSystemStatus() {
            fetch('/api/stats')
                .then(response => response.json())
                .then(data => {
                    const statusElement = document.getElementById('system-status');
                    const indicator = statusElement.querySelector('.status-indicator');
                    
                    // تحديث حالة النظام
                    indicator.className = 'status-indicator';
                    if (data.status === 'running') {
                        indicator.classList.add('status-running');
                        statusElement.innerHTML = '<span class="status-indicator status-running"></span> النظام يعمل';
                    } else if (data.status === 'stopped') {
                        indicator.classList.add('status-stopped');
                        statusElement.innerHTML = '<span class="status-indicator status-stopped"></span> النظام متوقف';
                    } else {
                        indicator.classList.add('status-error');
                        statusElement.innerHTML = '<span class="status-indicator status-error"></span> خطأ في النظام';
                    }
                })
                .catch(error => {
                    console.error('خطأ في تحديث حالة النظام:', error);
                });
        }
        
        // تحديث فوري عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', updateSystemStatus);
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
