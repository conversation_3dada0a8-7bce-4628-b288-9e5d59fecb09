# 🎤 دليل أولوية ElevenLabs TTS

## ✅ التحديثات المطبقة

### 🔑 **مفاتيح API الجديدة**
تم إضافة 3 مفاتيح ElevenLabs بترتيب الأولوية:

1. `***************************************************` (الأولوية الأولى)
2. `***************************************************` (الأولوية الثانية)  
3. `***************************************************` (الأولوية الثالثة)

### 🎵 **الصوت المجاني**
- **معرف الصوت**: `ErXwobaYiN019PkySvjV`
- **الاستخدام**: أولوية قصوى قبل أي صوت آخر
- **الجودة**: عالية ومناسبة للمحتوى

### 🎯 **نظام الأولوية الجديد**
```
1. ElevenLabs (أولوية قصوى دائماً)
   ├── المفتاح الأول
   ├── المفتاح الثاني  
   └── المفتاح الثالث
2. GoogleTranslate (بديل أول)
3. AWSPolly (بديل ثاني)
4. pyttsx (بديل أخير)
```

## 🔧 الملفات المحدثة

### 1. **`TTS/elevenlabs.py`**
- ✅ إضافة المفاتيح الجديدة
- ✅ نظام fallback ذكي
- ✅ استخدام الصوت المجاني أولاً
- ✅ معالجة أخطاء محسنة
- ✅ اختبار المفاتيح تلقائياً

### 2. **`config.toml`**
- ✅ تحديث مفاتيح API
- ✅ تعيين ElevenLabs كأولوية أولى
- ✅ إعداد الصوت المجاني
- ✅ تحسين إعدادات الـ fallback

### 3. **`TTS/smart_tts_manager.py`**
- ✅ ElevenLabs أولوية قصوى دائماً
- ✅ إدارة ذكية للمفاتيح
- ✅ تتبع استخدام المفاتيح
- ✅ إشعارات Telegram للأخطاء

### 4. **`TTS/engine_wrapper.py`**
- ✅ استخدام النظام الذكي دائماً
- ✅ إشعارات بدء TTS
- ✅ معالجة أخطاء محسنة

## 🚀 كيفية العمل

### **سير العمل الجديد:**

```
🎤 بدء TTS
    ↓
🔑 محاولة المفتاح الأول (ElevenLabs)
    ↓
🎵 استخدام الصوت المجاني ErXwobaYiN019PkySvjV
    ↓
✅ نجح؟ → إنهاء
❌ فشل؟ → المفتاح التالي
    ↓
🔑 محاولة المفتاح الثاني
    ↓
✅ نجح؟ → إنهاء  
❌ فشل؟ → المفتاح التالي
    ↓
🔑 محاولة المفتاح الثالث
    ↓
✅ نجح؟ → إنهاء
❌ فشل؟ → البديل (GoogleTranslate)
    ↓
🔄 البدائل: GTTS → pyttsx
```

## 🧪 الاختبار

### **تشغيل اختبار شامل:**
```bash
python test_elevenlabs_priority.py
```

### **الاختبارات المتضمنة:**
- ✅ استيراد ElevenLabs
- ✅ اختبار مفاتيح API
- ✅ اختبار الصوت المجاني
- ✅ إنشاء TTS فعلي
- ✅ مدير TTS الذكي
- ✅ إعدادات التكوين

## 📊 المميزات الجديدة

### 1. **نظام Fallback ذكي**
- محاولة المفاتيح بالترتيب
- تتبع المفاتيح المنتهية الرصيد
- تبديل تلقائي للبدائل

### 2. **إدارة الأخطاء المحسنة**
- تصنيف أنواع الأخطاء
- إشعارات Telegram مفصلة
- سجلات مفصلة للتشخيص

### 3. **مراقبة الاستخدام**
- تتبع استخدام كل مفتاح
- إحصائيات مفصلة
- تحذيرات انتهاء الرصيد

### 4. **الصوت المجاني المحسن**
- جودة عالية
- سرعة استجابة ممتازة
- مناسب للمحتوى الإنجليزي

## ⚙️ الإعدادات المهمة

### **في `config.toml`:**

```toml
[settings.tts]
voice_choice = "ElevenLabs"
elevenlabs_voice_id = "ErXwobaYiN019PkySvjV"
elevenlabs_api_key = "***************************************************"

[settings.tts.api_keys]
elevenlabs_keys = [
    "***************************************************",
    "***************************************************", 
    "***************************************************"
]

[settings.tts.priority_order]
primary = ["ElevenLabs", "GoogleTranslate", "AWSPolly", "pyttsx"]

[settings.tts.elevenlabs]
free_voice_id = "ErXwobaYiN019PkySvjV"
use_free_voice_first = true
```

## 📱 إشعارات Telegram

النظام الآن يرسل إشعارات مفصلة:

### **إشعارات النجاح:**
```
🎤 بدء تحويل النص إلى صوت باستخدام ElevenLabs
✅ تم إنشاء الصوت بنجاح مع المفتاح 1
```

### **إشعارات الأخطاء:**
```
⚠️ انتهى رصيد ElevenLabs API Key: sk_3d7cdc9...
🔄 التبديل إلى المفتاح التالي
```

### **إشعارات البدائل:**
```
❌ فشلت جميع مفاتيح ElevenLabs
🔄 التبديل إلى GoogleTranslate
```

## 🔍 استكشاف الأخطاء

### **مشكلة: لا يستخدم ElevenLabs**
**الحل:**
```bash
# فحص الإعدادات
python test_elevenlabs_priority.py

# التأكد من الإعدادات
grep -A 5 "voice_choice" config.toml
```

### **مشكلة: المفاتيح لا تعمل**
**الحل:**
```bash
# اختبار المفاتيح
python -c "
from TTS.elevenlabs import elevenlabs
engine = elevenlabs()
for i, key in enumerate(engine.api_keys):
    print(f'Key {i+1}: {engine.test_api_key(key)}')
"
```

### **مشكلة: الصوت المجاني لا يعمل**
**الحل:**
- تحقق من معرف الصوت: `ErXwobaYiN019PkySvjV`
- تأكد من صحة المفاتيح
- جرب صوت آخر إذا لزم الأمر

## 📈 الأداء المتوقع

مع النظام الجديد:
- **سرعة أعلى**: ElevenLabs أسرع من البدائل
- **جودة أفضل**: صوت طبيعي وواضح
- **موثوقية عالية**: 3 مفاتيح + نظام fallback
- **مراقبة ذكية**: تتبع الاستخدام والأخطاء

## 🎯 النتيجة النهائية

الآن النظام:
1. **يستخدم ElevenLabs أولاً دائماً**
2. **يجرب 3 مفاتيح بالترتيب**
3. **يستخدم الصوت المجاني المحدد**
4. **يتبدل للبدائل عند الحاجة**
5. **يرسل إشعارات مفصلة**
6. **يتتبع الاستخدام والأخطاء**

---

## 🚀 للتشغيل الآن:

```bash
# اختبار النظام
python test_elevenlabs_priority.py

# تشغيل النظام الكامل
python start_full_system.py

# أو تشغيل فيديو واحد للاختبار
python main.py
```

**🎉 استمتع بجودة ElevenLabs العالية مع نظام fallback ذكي!**
