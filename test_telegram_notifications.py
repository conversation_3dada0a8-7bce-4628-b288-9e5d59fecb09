#!/usr/bin/env python3
"""
اختبار نظام الإشعارات عبر التيليجرام
"""

import asyncio
import logging
from utils.smart_config_checker import check_config_with_telegram_notifications
from automation.config_notifications import (
    notify_tiktok_session_missing,
    notify_elevenlabs_api_missing,
    notify_auto_fix_success
)

# إعداد السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_config_notifications():
    """اختبار إشعارات الإعدادات"""
    print("🧪 اختبار نظام الإشعارات...")
    
    try:
        # اختبار إشعار TikTok
        print("📱 اختبار إشعار TikTok...")
        await notify_tiktok_session_missing()
        await asyncio.sleep(2)
        
        # اختبار إشعار ElevenLabs
        print("🎤 اختبار إشعار ElevenLabs...")
        await notify_elevenlabs_api_missing()
        await asyncio.sleep(2)
        
        # اختبار إشعار النجاح
        print("✅ اختبار إشعار النجاح...")
        await notify_auto_fix_success("switched_to_elevenlabs_free")
        
        print("✅ تم إرسال جميع الإشعارات التجريبية!")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")

def test_config_checker():
    """اختبار فاحص الإعدادات"""
    print("\n🔍 اختبار فاحص الإعدادات...")
    
    try:
        # فحص الإعدادات الحالية
        is_valid = check_config_with_telegram_notifications()
        
        if is_valid:
            print("✅ الإعدادات صحيحة!")
        else:
            print("⚠️ وجدت مشاكل في الإعدادات - تم إرسال إشعارات")
        
    except Exception as e:
        print(f"❌ خطأ في فحص الإعدادات: {str(e)}")

async def main():
    """الدالة الرئيسية للاختبار"""
    print("""
    🧪 اختبار نظام الإشعارات الذكي
    ==================================
    
    سيتم إرسال إشعارات تجريبية إلى بوت التيليجرام
    تأكد من تشغيل مدير التيليجرام أولاً:
    python start_telegram_manager.py
    
    """)
    
    # اختبار الإشعارات
    await test_config_notifications()
    
    # اختبار فاحص الإعدادات
    test_config_checker()
    
    print("""
    ==================================
    ✅ انتهى الاختبار!
    
    تحقق من بوت التيليجرام لرؤية الإشعارات
    """)

if __name__ == "__main__":
    asyncio.run(main())
