#!/usr/bin/env python3
"""
تطبيق App Engine محسن لـ Google Cloud Free Tier
يعمل كواجهة تحكم خفيفة ومنسق للمهام
"""

import os
import json
import logging
from datetime import datetime
from flask import Flask, render_template, request, jsonify
from google.cloud import storage, functions_v1
import requests

# إعداد التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'reddit-video-maker-free-tier-2024'

# إعداد السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# إعدادات Free Tier
FREE_TIER_LIMITS = {
    'daily_f1_hours': 28,
    'daily_b1_hours': 9,
    'storage_gb': 1,
    'egress_gb': 10,
    'max_concurrent_requests': 1
}

class FreeTierMonitor:
    """مراقب استخدام Free Tier"""
    
    def __init__(self):
        self.project_id = os.environ.get('GOOGLE_CLOUD_PROJECT')
        self.storage_client = storage.Client()
        
    def get_usage_stats(self):
        """الحصول على إحصائيات الاستخدام"""
        try:
            stats = {
                'timestamp': datetime.now().isoformat(),
                'storage_usage_mb': self._get_storage_usage(),
                'function_invocations_today': self._get_function_invocations(),
                'app_engine_hours_today': self._get_app_engine_hours(),
                'status': 'healthy'
            }
            
            # فحص الحدود
            if stats['storage_usage_mb'] > 800:  # 800 MB من أصل 1 GB
                stats['warnings'] = stats.get('warnings', [])
                stats['warnings'].append('اقتراب من حد التخزين')
                
            return stats
        except Exception as e:
            logger.error(f"خطأ في الحصول على الإحصائيات: {e}")
            return {'status': 'error', 'error': str(e)}
            
    def _get_storage_usage(self):
        """حساب استخدام التخزين"""
        try:
            total_size = 0
            buckets = [
                f"{self.project_id}-video-assets",
                f"{self.project_id}-video-results"
            ]
            
            for bucket_name in buckets:
                try:
                    bucket = self.storage_client.bucket(bucket_name)
                    for blob in bucket.list_blobs():
                        total_size += blob.size
                except Exception:
                    continue  # تجاهل الأخطاء في buckets غير موجودة
                    
            return total_size / (1024 * 1024)  # تحويل إلى MB
        except Exception as e:
            logger.error(f"خطأ في حساب التخزين: {e}")
            return 0
            
    def _get_function_invocations(self):
        """حساب استدعاءات Cloud Functions (تقديري)"""
        # في التطبيق الحقيقي، استخدم Cloud Monitoring API
        return 0
        
    def _get_app_engine_hours(self):
        """حساب ساعات App Engine (تقديري)"""
        # في التطبيق الحقيقي، استخدم Cloud Monitoring API
        return 0

# إنشاء مثيل المراقب
monitor = FreeTierMonitor()

@app.route('/')
def dashboard():
    """لوحة التحكم الرئيسية"""
    try:
        stats = monitor.get_usage_stats()
        return render_template('free_tier_dashboard.html', stats=stats)
    except Exception as e:
        logger.error(f"خطأ في لوحة التحكم: {e}")
        return f"خطأ: {e}", 500

@app.route('/api/stats')
def api_stats():
    """API للحصول على الإحصائيات"""
    try:
        stats = monitor.get_usage_stats()
        return jsonify(stats)
    except Exception as e:
        logger.error(f"خطأ في API الإحصائيات: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/create-video', methods=['POST'])
def create_video():
    """إنشاء فيديو جديد عبر Cloud Function"""
    try:
        # فحص الحدود قبل المعالجة
        stats = monitor.get_usage_stats()
        if stats.get('storage_usage_mb', 0) > 900:  # 900 MB
            return jsonify({
                'error': 'اقتراب من حد التخزين المجاني',
                'suggestion': 'يرجى تنظيف الملفات القديمة'
            }), 429
            
        # الحصول على البيانات
        request_data = request.get_json() or {}
        video_type = request_data.get('type', 'default')
        
        logger.info(f"🎬 طلب إنشاء فيديو: {video_type}")
        
        # استدعاء Cloud Function
        function_url = f"https://us-central1-{monitor.project_id}.cloudfunctions.net/video-processor"
        
        response = requests.post(
            function_url,
            json={
                'type': video_type,
                'upload_youtube': True,
                'source': 'app_engine'
            },
            timeout=600  # 10 دقائق
        )
        
        if response.status_code == 200:
            result = response.json()
            logger.info(f"✅ تم إنشاء الفيديو بنجاح: {result}")
            
            return jsonify({
                'success': True,
                'message': 'تم إنشاء الفيديو بنجاح',
                'result': result
            })
        else:
            logger.error(f"❌ فشل في إنشاء الفيديو: {response.text}")
            return jsonify({
                'error': 'فشل في إنشاء الفيديو',
                'details': response.text
            }), 500
            
    except Exception as e:
        logger.error(f"خطأ في إنشاء الفيديو: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/cleanup', methods=['POST'])
def cleanup_storage():
    """تنظيف التخزين لتوفير المساحة"""
    try:
        cleaned_size = 0
        cleaned_files = 0
        
        # تنظيف الملفات القديمة
        buckets = [
            f"{monitor.project_id}-video-assets",
            f"{monitor.project_id}-video-results"
        ]
        
        for bucket_name in buckets:
            try:
                bucket = monitor.storage_client.bucket(bucket_name)
                
                # حذف الملفات الأقدم من يوم واحد
                cutoff_time = datetime.now().timestamp() - (24 * 60 * 60)
                
                for blob in bucket.list_blobs():
                    if blob.time_created.timestamp() < cutoff_time:
                        cleaned_size += blob.size
                        blob.delete()
                        cleaned_files += 1
                        
            except Exception as e:
                logger.warning(f"تحذير في تنظيف {bucket_name}: {e}")
                continue
                
        logger.info(f"🗑️ تم تنظيف {cleaned_files} ملف، توفير {cleaned_size / (1024*1024):.1f} MB")
        
        return jsonify({
            'success': True,
            'cleaned_files': cleaned_files,
            'cleaned_size_mb': cleaned_size / (1024 * 1024),
            'message': f'تم تنظيف {cleaned_files} ملف'
        })
        
    except Exception as e:
        logger.error(f"خطأ في التنظيف: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/health')
def health_check():
    """فحص صحة التطبيق"""
    try:
        stats = monitor.get_usage_stats()
        
        health_status = {
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'free_tier_usage': {
                'storage_usage_percent': (stats.get('storage_usage_mb', 0) / 1024) * 100,
                'within_limits': stats.get('storage_usage_mb', 0) < 1000
            }
        }
        
        # فحص الحدود
        if health_status['free_tier_usage']['storage_usage_percent'] > 90:
            health_status['status'] = 'warning'
            health_status['message'] = 'اقتراب من حد التخزين المجاني'
            
        return jsonify(health_status)
        
    except Exception as e:
        return jsonify({
            'status': 'error',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/free-tier-status')
def free_tier_status():
    """حالة استخدام Free Tier"""
    try:
        stats = monitor.get_usage_stats()
        
        status = {
            'storage': {
                'used_mb': stats.get('storage_usage_mb', 0),
                'limit_mb': 1024,  # 1 GB
                'percentage': (stats.get('storage_usage_mb', 0) / 1024) * 100
            },
            'functions': {
                'invocations_today': stats.get('function_invocations_today', 0),
                'limit_monthly': 2000000,  # 2 مليون
                'percentage': (stats.get('function_invocations_today', 0) / 2000000) * 100
            },
            'app_engine': {
                'hours_today': stats.get('app_engine_hours_today', 0),
                'limit_daily': 28,  # 28 ساعة F1
                'percentage': (stats.get('app_engine_hours_today', 0) / 28) * 100
            },
            'overall_status': 'healthy'
        }
        
        # تحديد الحالة العامة
        max_percentage = max(
            status['storage']['percentage'],
            status['functions']['percentage'],
            status['app_engine']['percentage']
        )
        
        if max_percentage > 90:
            status['overall_status'] = 'critical'
        elif max_percentage > 75:
            status['overall_status'] = 'warning'
            
        return jsonify(status)
        
    except Exception as e:
        logger.error(f"خطأ في حالة Free Tier: {e}")
        return jsonify({'error': str(e)}), 500

# معالج الأخطاء
@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'الصفحة غير موجودة'}), 404

@app.errorhandler(500)
def internal_error(error):
    logger.error(f"خطأ داخلي: {error}")
    return jsonify({'error': 'خطأ داخلي في الخادم'}), 500

# نقطة دخول App Engine
if __name__ == '__main__':
    # للتطوير المحلي فقط
    app.run(host='127.0.0.1', port=8080, debug=True)
else:
    # للإنتاج على App Engine
    logger.info("🚀 تم تشغيل تطبيق App Engine")
    
    # إرسال إشعار بدء التشغيل
    try:
        # يمكن إضافة إشعار Telegram هنا
        logger.info("✅ تطبيق Free Tier جاهز للعمل")
    except Exception as e:
        logger.warning(f"تحذير في بدء التشغيل: {e}")
