#!/usr/bin/env python3
"""
إصلاح سريع لإعداد التلغرام
يحل مشكلة معرف المحادثة ويختبر الاتصال
"""

import requests
import time
import os

def main():
    """إصلاح سريع لإعداد التلغرام"""
    print("""
    🔧 إصلاح سريع لإعداد التلغرام
    ===============================
    
    سيتم حل مشكلة معرف المحادثة وإعداد البوت بشكل صحيح.
    
    """)
    
    # الحصول على رمز البوت
    try:
        import toml
        with open("config.toml", "r", encoding="utf-8") as f:
            config = toml.load(f)
        bot_token = config["telegram"]["bot_token"]
        print(f"✅ رمز البوت: {bot_token[:10]}...")
    except Exception as e:
        print(f"❌ خطأ في قراءة رمز البوت: {e}")
        return
    
    # اختبار الاتصال بالبوت
    print("\n🧪 اختبار الاتصال بالبوت...")
    try:
        url = f"https://api.telegram.org/bot{bot_token}/getMe"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data['ok']:
                bot_info = data['result']
                print(f"✅ البوت متصل: @{bot_info['username']}")
                print(f"   اسم البوت: {bot_info['first_name']}")
            else:
                print(f"❌ خطأ في البوت: {data}")
                return
        else:
            print(f"❌ خطأ HTTP: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return
    
    # البحث عن معرف المحادثة
    print(f"\n🔍 البحث عن معرف المحادثة...")
    print(f"💡 إذا لم يتم العثور على معرف:")
    print(f"   1. ابحث عن البوت: @{bot_info['username']}")
    print(f"   2. أرسل /start أو أي رسالة")
    print(f"   3. أعد تشغيل هذا الأداة")
    
    try:
        url = f"https://api.telegram.org/bot{bot_token}/getUpdates"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data['ok'] and data['result']:
                # البحث عن آخر رسالة
                for update in reversed(data['result']):
                    if 'message' in update:
                        chat_id = str(update['message']['chat']['id'])
                        username = update['message']['from'].get('username', 'غير متوفر')
                        first_name = update['message']['from'].get('first_name', 'غير متوفر')
                        
                        print(f"\n✅ تم العثور على محادثة:")
                        print(f"   معرف المحادثة: {chat_id}")
                        print(f"   اسم المستخدم: @{username}")
                        print(f"   الاسم الأول: {first_name}")
                        
                        # حفظ معرف المحادثة
                        with open("telegram_chat_id.txt", "w") as f:
                            f.write(chat_id)
                        print(f"✅ تم حفظ معرف المحادثة")
                        
                        # اختبار إرسال رسالة
                        print(f"\n🧪 اختبار إرسال رسالة...")
                        test_url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
                        test_data = {
                            'chat_id': chat_id,
                            'text': '🎉 تم إصلاح إعداد التلغرام بنجاح!\n\n✅ البوت يعمل الآن بشكل صحيح\n💡 يمكنك استخدام /start للوصول للقائمة الرئيسية'
                        }
                        test_response = requests.post(test_url, data=test_data, timeout=10)
                        
                        if test_response.status_code == 200:
                            print("✅ تم إرسال رسالة اختبار بنجاح!")
                            print("\n🎉 تم إصلاح الإعداد بنجاح!")
                            print("🚀 يمكنك الآن تشغيل النظام:")
                            print("   python start_enhanced_system.py")
                        else:
                            print(f"❌ فشل في إرسال رسالة الاختبار: {test_response.status_code}")
                        
                        return
                        
                print("\n❌ لم يتم العثور على رسائل")
                print("💡 يرجى إرسال رسالة للبوت أولاً")
            else:
                print(f"\n❌ لا توجد رسائل: {data}")
        else:
            print(f"❌ خطأ في الحصول على التحديثات: {response.status_code}")
            
    except Exception as e:
        print(f"❌ خطأ في البحث عن معرف المحادثة: {e}")

if __name__ == "__main__":
    main()
