#!/usr/bin/env python3
"""
إعداد البوت الجديد بسرعة
يعد البوت الجديد ويختبر الاتصال
"""

import requests
import time

def main():
    """إعداد البوت الجديد"""
    print("""
    🤖 إعداد البوت الجديد
    ====================
    
    البوت: @sah8fqwuhfu_bot
    
    """)
    
    # معلومات البوت
    bot_token = "8172189631:AAEb-0eRqHfy8M2dDjpJdeGHx4fjyczsR3w"
    bot_username = "@sah8fqwuhfu_bot"
    
    print(f"🤖 البوت: {bot_username}")
    
    # اختبار الاتصال
    print("\n🧪 اختبار الاتصال...")
    try:
        url = f"https://api.telegram.org/bot{bot_token}/getMe"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data['ok']:
                bot_info = data['result']
                print(f"✅ البوت متصل: @{bot_info['username']}")
                print(f"   اسم البوت: {bot_info['first_name']}")
            else:
                print(f"❌ خطأ في البوت: {data}")
                return
        else:
            print(f"❌ خطأ HTTP: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return
    
    # البحث عن معرف المحادثة
    print(f"\n🔍 البحث عن معرف المحادثة...")
    try:
        url = f"https://api.telegram.org/bot{bot_token}/getUpdates"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data['ok'] and data['result']:
                # البحث عن آخر رسالة
                for update in reversed(data['result']):
                    if 'message' in update:
                        chat_id = str(update['message']['chat']['id'])
                        username = update['message']['from'].get('username', 'غير متوفر')
                        first_name = update['message']['from'].get('first_name', 'غير متوفر')
                        
                        print(f"\n✅ تم العثور على محادثة:")
                        print(f"   معرف المحادثة: {chat_id}")
                        print(f"   اسم المستخدم: @{username}")
                        print(f"   الاسم الأول: {first_name}")
                        
                        # حفظ معرف المحادثة
                        with open("telegram_chat_id.txt", "w") as f:
                            f.write(chat_id)
                        print(f"✅ تم حفظ معرف المحادثة")
                        
                        # اختبار إرسال رسالة
                        print(f"\n🧪 اختبار إرسال رسالة...")
                        test_url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
                        
                        # إرسال رسالة ترحيب مع الأزرار
                        welcome_message = """🎉 تم إعداد البوت المحسن بنجاح!

✅ النظام جاهز للعمل مع المميزات الجديدة:

🤖 **بوت تفاعلي** مع أزرار تشغيل وإيقاف
📱 **نقل جميع الرسائل** للتلغرام
🔄 **مراقبة مستمرة** للعمليات
📊 **إشعارات مفصلة** لكل خطوة

💡 استخدم /start للوصول للقائمة الرئيسية"""
                        
                        test_data = {
                            'chat_id': chat_id,
                            'text': welcome_message
                        }
                        test_response = requests.post(test_url, data=test_data, timeout=10)
                        
                        if test_response.status_code == 200:
                            print("✅ تم إرسال رسالة ترحيب بنجاح!")
                            
                            print(f"\n🎉 تم إعداد البوت بنجاح!")
                            print(f"🚀 يمكنك الآن:")
                            print(f"   1. استخدام /start في التلغرام")
                            print(f"   2. تشغيل النظام: python start_enhanced_system.py")
                            print(f"   3. أو تشغيل النظام الأساسي: python main.py")
                            
                        else:
                            print(f"❌ فشل في إرسال رسالة: {test_response.status_code}")
                        
                        return
                        
                print("\n❌ لم يتم العثور على رسائل")
                print(f"💡 يرجى:")
                print(f"   1. البحث عن البوت: {bot_username}")
                print(f"   2. إرسال /start أو أي رسالة")
                print(f"   3. إعادة تشغيل هذا الأداة")
            else:
                print(f"\n❌ لا توجد رسائل")
                print(f"💡 يرجى إرسال رسالة للبوت: {bot_username}")
        else:
            print(f"❌ خطأ في الحصول على التحديثات: {response.status_code}")
            
    except Exception as e:
        print(f"❌ خطأ في البحث عن معرف المحادثة: {e}")

if __name__ == "__main__":
    main()
