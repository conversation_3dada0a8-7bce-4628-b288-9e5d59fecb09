#!/usr/bin/env python3
"""
إضافة فيديو خلفية مخصص
"""

import json
from pathlib import Path

def add_custom_background(video_path: str, name: str = "custom"):
    """إضافة فيديو خلفية مخصص"""
    
    # التحقق من وجود الفيديو
    if not Path(video_path).exists():
        print(f"❌ الفيديو غير موجود: {video_path}")
        return False
    
    # إنشاء مجلد الخلفيات
    bg_dir = Path("assets/backgrounds/video")
    bg_dir.mkdir(parents=True, exist_ok=True)
    
    # نسخ الفيديو إلى مجلد الخلفيات
    import shutil
    target_path = bg_dir / f"{name}-background.mp4"
    shutil.copy2(video_path, target_path)
    print(f"✅ تم نسخ الفيديو إلى: {target_path}")
    
    # تحديث ملف background_videos.json
    bg_json_path = Path("utils/background_videos.json")
    
    if bg_json_path.exists():
        with open(bg_json_path, 'r', encoding='utf-8') as f:
            backgrounds = json.load(f)
    else:
        backgrounds = {"__comment": "Supported Backgrounds. Can add/remove background video here..."}
    
    # إضافة الفيديو الجديد
    backgrounds[name] = [
        "local_file",  # لا يحتاج تحميل
        f"{name}-background.mp4",
        "Custom User",
        "center"
    ]
    
    # حفظ التحديث
    with open(bg_json_path, 'w', encoding='utf-8') as f:
        json.dump(backgrounds, f, indent=4, ensure_ascii=False)
    
    print(f"✅ تم إضافة '{name}' إلى قائمة الخلفيات")
    
    # تحديث config.toml
    import toml
    config_path = Path("config.toml")
    
    if config_path.exists():
        with open(config_path, 'r', encoding='utf-8') as f:
            config = toml.load(f)
        
        config["settings"]["background"]["background_video"] = name
        
        with open(config_path, 'w', encoding='utf-8') as f:
            toml.dump(config, f)
        
        print(f"✅ تم تحديث الإعدادات لاستخدام '{name}'")
    
    return True

def list_current_backgrounds():
    """عرض الخلفيات المتاحة حالياً"""
    bg_json_path = Path("utils/background_videos.json")
    
    if not bg_json_path.exists():
        print("❌ ملف الخلفيات غير موجود")
        return
    
    with open(bg_json_path, 'r', encoding='utf-8') as f:
        backgrounds = json.load(f)
    
    print("\n📋 الخلفيات المتاحة:")
    print("=" * 50)
    
    for name, details in backgrounds.items():
        if name == "__comment":
            continue
        
        url, filename, credit, position = details
        size_estimate = "غير معروف"
        
        # تقدير الحجم بناءً على المصدر
        if "minecraft" in name:
            size_estimate = "كبير (1-2GB)"
        elif "gta" in name or "motor" in name:
            size_estimate = "كبير (800MB-1.2GB)"
        elif "rocket" in name:
            size_estimate = "متوسط (600-800MB)"
        elif "fall" in name or "cluster" in name:
            size_estimate = "صغير (200-400MB)"
        else:
            size_estimate = "متوسط (400-600MB)"
        
        print(f"🎮 {name}")
        print(f"   📁 {filename}")
        print(f"   👤 {credit}")
        print(f"   📊 {size_estimate}")
        print()

if __name__ == "__main__":
    print("🎬 إدارة فيديوهات الخلفية")
    print("=" * 30)
    
    # عرض الخلفيات المتاحة
    list_current_backgrounds()
    
    # خيار إضافة فيديو مخصص
    choice = input("\nهل تريد إضافة فيديو مخصص؟ (y/n): ").lower()
    
    if choice == 'y':
        video_path = input("مسار الفيديو: ")
        name = input("اسم الخلفية (اختياري، افتراضي: custom): ").strip()
        
        if not name:
            name = "custom"
        
        if add_custom_background(video_path, name):
            print(f"\n🎉 تم! يمكنك الآن تشغيل البوت وسيستخدم '{name}' كخلفية")
        else:
            print("\n❌ فشل في إضافة الفيديو")
    else:
        print("\n💡 لتغيير الخلفية، عدّل 'background_video' في config.toml")
        print("   الخيارات الأصغر حجماً: fall-guys, cluster-truck, csgo-surf")
