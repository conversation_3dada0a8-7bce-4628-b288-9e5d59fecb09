# 🤖 إعداد معرف المحادثة (Chat ID)

## المشكلة
البوت يحتاج إلى معرف المحادثة (Chat ID) لإرسال الإشعارات عبر التيليجرام.

## الحل السريع

### الطريقة الأولى: استخدام الأداة التلقائية
```bash
python get_chat_id.py
```

### الطريقة الثانية: يدوياً

1. **ابحث عن البوت في التيليجرام:**
   - اذهب إلى التيليجرام
   - ابحث عن: `@sah8fqwuhfu_bot`
   - أو استخدم الرابط: https://t.me/sah8fqwuhfu_bot

2. **أرسل رسالة للبوت:**
   - اضغط "Start" أو أرسل `/start`
   - أو أرسل أي رسالة مثل "مرحبا"

3. **احصل على معرف المحادثة:**
   ```bash
   python get_chat_id.py
   ```

4. **تأكد من الإعداد:**
   - سيتم حفظ المعرف في `telegram_chat_id.txt`
   - سيتم تحديث `config.toml` تلقائياً
   - ستصلك رسالة اختبار في التيليجرام

## التحقق من الإعداد

بعد الحصول على معرف المحادثة، يمكنك تشغيل البوت:

```bash
python main.py
```

إذا كان الإعداد صحيحاً، ستتوقف رسائل الخطأ التالية:
- "لم يتم العثور على رسائل لاكتشاف معرف المحادثة"
- "لا يمكن إرسال الرسالة: معرف المحادثة غير متوفر"

## استكشاف الأخطاء

### إذا لم تجد البوت:
- تأكد من رمز البوت في `config.toml`
- تحقق من اتصال الإنترنت

### إذا لم يتم العثور على رسائل:
- تأكد من إرسال رسالة للبوت أولاً
- انتظر دقيقة وأعد المحاولة

### إذا فشل الاختبار:
- تحقق من رمز البوت
- تأكد من أن البوت نشط

## ملاحظات مهمة

- معرف المحادثة فريد لكل مستخدم
- يجب الحصول عليه مرة واحدة فقط
- يتم حفظه تلقائياً للاستخدام المستقبلي
- البوت سيرسل إشعارات لهذا المعرف فقط

## الملفات المتأثرة

- `telegram_chat_id.txt` - يحتوي على معرف المحادثة
- `config.toml` - الإعدادات الرئيسية
- `get_chat_id.py` - أداة الحصول على المعرف
