#!/usr/bin/env python3
"""
نظام بديل لالتقاط لقطات الشاشة
يستخدم عند فشل النظام الأساسي
"""

import os
import time
import requests
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont
from utils.console import print_substep

def create_text_image(text: str, output_path: str, width: int = 1080, height: int = 400):
    """إنشاء صورة نصية بديلة"""
    try:
        # إنشاء صورة بخلفية داكنة
        img = Image.new('RGB', (width, height), color=(33, 33, 36))
        draw = ImageDraw.Draw(img)
        
        # محاولة استخدام خط مناسب
        try:
            font = ImageFont.truetype("arial.ttf", 40)
        except:
            try:
                font = ImageFont.truetype("fonts/Roboto-Regular.ttf", 40)
            except:
                font = ImageFont.load_default()
        
        # تقسيم النص إلى أسطر
        words = text.split()
        lines = []
        current_line = ""
        
        for word in words:
            test_line = current_line + " " + word if current_line else word
            bbox = draw.textbbox((0, 0), test_line, font=font)
            if bbox[2] - bbox[0] < width - 40:  # هامش 20 بكسل من كل جانب
                current_line = test_line
            else:
                if current_line:
                    lines.append(current_line)
                current_line = word
        
        if current_line:
            lines.append(current_line)
        
        # رسم النص
        y_offset = (height - len(lines) * 50) // 2
        for i, line in enumerate(lines):
            bbox = draw.textbbox((0, 0), line, font=font)
            x = (width - (bbox[2] - bbox[0])) // 2
            y = y_offset + i * 50
            draw.text((x, y), line, fill=(255, 255, 255), font=font)
        
        # حفظ الصورة
        Path(output_path).parent.mkdir(parents=True, exist_ok=True)
        img.save(output_path)
        return True
        
    except Exception as e:
        print_substep(f"فشل في إنشاء صورة نصية: {str(e)}", style="red")
        return False

def create_fallback_screenshots(reddit_object: dict, screenshot_num: int):
    """إنشاء لقطات شاشة بديلة باستخدام النصوص"""
    reddit_id = reddit_object["thread_id"]
    
    print_substep("إنشاء لقطات شاشة بديلة...", style="yellow")
    
    # إنشاء مجلد الحفظ
    save_path = Path(f"assets/temp/{reddit_id}/png")
    save_path.mkdir(parents=True, exist_ok=True)
    
    # إنشاء صورة العنوان
    title_text = reddit_object.get("thread_title", "عنوان المنشور")
    title_path = save_path / "title.png"
    
    if create_text_image(title_text, str(title_path), width=1080, height=600):
        print_substep("تم إنشاء صورة العنوان", style="green")
    else:
        print_substep("فشل في إنشاء صورة العنوان", style="red")
    
    # إنشاء صور التعليقات
    comments = reddit_object.get("comments", [])
    created_count = 0
    
    for idx, comment in enumerate(comments[:screenshot_num]):
        comment_text = comment.get("comment_body", f"تعليق رقم {idx + 1}")
        comment_path = save_path / f"comment_{idx}.png"
        
        if create_text_image(comment_text, str(comment_path), width=1080, height=400):
            created_count += 1
            print_substep(f"تم إنشاء صورة التعليق {idx + 1}", style="green")
        else:
            print_substep(f"فشل في إنشاء صورة التعليق {idx + 1}", style="red")
    
    print_substep(f"تم إنشاء {created_count} صورة بديلة من أصل {screenshot_num}", style="bold green")
    return created_count > 0

def test_reddit_connection():
    """اختبار الاتصال بـ Reddit"""
    try:
        response = requests.get("https://www.reddit.com", timeout=10)
        return response.status_code == 200
    except:
        return False

def diagnose_screenshot_issues():
    """تشخيص مشاكل لقطات الشاشة"""
    print_substep("تشخيص مشاكل لقطات الشاشة...", style="yellow")
    
    issues = []
    
    # فحص الاتصال بالإنترنت
    if not test_reddit_connection():
        issues.append("مشكلة في الاتصال بـ Reddit")
    
    # فحص Playwright
    try:
        from playwright.sync_api import sync_playwright
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            browser.close()
    except Exception as e:
        issues.append(f"مشكلة في Playwright: {str(e)}")
    
    # فحص المجلدات
    required_dirs = ["assets/temp", "video_creation/data"]
    for dir_path in required_dirs:
        if not Path(dir_path).exists():
            issues.append(f"مجلد مفقود: {dir_path}")
    
    # فحص ملفات cookies
    cookie_files = [
        "video_creation/data/cookie-dark-mode.json",
        "video_creation/data/cookie-light-mode.json"
    ]
    for cookie_file in cookie_files:
        if not Path(cookie_file).exists():
            issues.append(f"ملف cookies مفقود: {cookie_file}")
    
    if issues:
        print_substep("تم العثور على المشاكل التالية:", style="red")
        for issue in issues:
            print_substep(f"  • {issue}", style="red")
    else:
        print_substep("لم يتم العثور على مشاكل واضحة", style="green")
    
    return issues

if __name__ == "__main__":
    # اختبار النظام البديل
    test_reddit_object = {
        "thread_id": "test123",
        "thread_title": "هذا اختبار للنظام البديل لالتقاط لقطات الشاشة",
        "comments": [
            {"comment_body": "هذا تعليق اختبار رقم 1"},
            {"comment_body": "هذا تعليق اختبار رقم 2"},
            {"comment_body": "هذا تعليق اختبار رقم 3"}
        ]
    }
    
    print("🧪 اختبار النظام البديل...")
    diagnose_screenshot_issues()
    create_fallback_screenshots(test_reddit_object, 3)
