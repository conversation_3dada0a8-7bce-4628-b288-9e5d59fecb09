#!/usr/bin/env python3
"""
تشغيل مدير التيليجرام للتحكم في البوت عن بُعد
"""

import asyncio
import logging
import sys
from pathlib import Path

# إعداد السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/telegram_manager.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

async def main():
    """تشغيل مدير التيليجرام"""
    print("""
    📱 مدير التيليجرام - Reddit Video Maker Bot
    =============================================
    
    ✅ تشغيل وإيقاف البوت عن بُعد
    ✅ إدارة مفاتيح APIs
    ✅ إعداد ElevenLabs المجاني
    ✅ مراقبة حالة النظام
    
    استخدم الأوامر التالية في التيليجرام:
    /start - القائمة الرئيسية
    /run_bot - تشغيل البوت
    /stop_bot - إيقاف البوت
    /status - حالة النظام
    /apis - إدارة APIs
    
    """)
    
    try:
        # استيراد مدير API
        from automation.telegram_api_manager import TelegramAPIManager
        from utils import settings
        
        # الحصول على رمز البوت
        bot_token = settings.config["telegram"]["bot_token"]
        
        # إنشاء مدير API
        manager = TelegramAPIManager(bot_token)
        
        logger.info("🚀 بدء تشغيل مدير التيليجرام...")
        
        # بدء البوت
        await manager.start_bot()
        
    except KeyboardInterrupt:
        logger.info("⏹️ تم إيقاف مدير التيليجرام بواسطة المستخدم")
    except Exception as e:
        logger.error(f"❌ خطأ في مدير التيليجرام: {str(e)}")
        raise

if __name__ == "__main__":
    # التحقق من إصدار Python
    if sys.version_info.major != 3 or sys.version_info.minor < 10:
        print("❌ يتطلب Python 3.10 أو أحدث")
        sys.exit(1)
    
    # إنشاء المجلدات المطلوبة
    Path("logs").mkdir(exist_ok=True)
    
    # تشغيل المدير
    asyncio.run(main())
