#!/usr/bin/env python3
"""
اختبار نظام مصادقة YouTube OAuth
يتحقق من جميع مكونات النظام
"""

import os
import sys
import json
import logging
from pathlib import Path
from datetime import datetime

# إضافة مجلد automation للمسار
sys.path.append(str(Path(__file__).parent / "automation"))

from automation.youtube_uploader import YouTubeTokenManager, YouTubeUploader
from automation.telegram_bot import get_bot

# إعداد السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def print_banner():
    """طباعة شعار الاختبار"""
    print("""
╔══════════════════════════════════════════════════════════════╗
║                  🧪 اختبار نظام YouTube OAuth                ║
║                   Reddit Video Maker Bot                    ║
╚══════════════════════════════════════════════════════════════╝
    """)

def test_client_secret_file():
    """اختبار ملف client_secret.json"""
    print("\n1️⃣ اختبار ملف client_secret.json...")
    
    client_secret_file = "client_secret.json"
    
    if not os.path.exists(client_secret_file):
        print(f"❌ ملف {client_secret_file} غير موجود")
        return False
        
    try:
        with open(client_secret_file, 'r') as f:
            data = json.load(f)
            
        # التحقق من البنية المطلوبة
        if 'installed' in data:
            required_fields = ['client_id', 'client_secret', 'auth_uri', 'token_uri']
            missing_fields = [field for field in required_fields 
                            if field not in data['installed']]
            
            if missing_fields:
                print(f"❌ حقول مفقودة في ملف client_secret: {missing_fields}")
                return False
                
            print(f"✅ ملف {client_secret_file} صحيح")
            print(f"   📋 Client ID: {data['installed']['client_id'][:20]}...")
            return True
        else:
            print(f"❌ بنية ملف {client_secret_file} غير صحيحة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في قراءة ملف {client_secret_file}: {e}")
        return False

def test_token_manager():
    """اختبار مدير التوكن"""
    print("\n2️⃣ اختبار مدير التوكن...")
    
    try:
        token_manager = YouTubeTokenManager()
        print("✅ تم إنشاء مدير التوكن بنجاح")
        
        # اختبار تحميل التوكن
        credentials = token_manager.load_credentials()
        if credentials:
            print("✅ تم العثور على توكن محفوظ")
            
            # التحقق من صلاحية التوكن
            if credentials.valid:
                print("✅ التوكن صالح")
                
                # عرض معلومات التوكن
                if credentials.expiry:
                    time_until_expiry = credentials.expiry - datetime.utcnow()
                    hours_until_expiry = time_until_expiry.total_seconds() / 3600
                    print(f"   ⏰ الوقت المتبقي: {hours_until_expiry:.1f} ساعة")
                else:
                    print("   ⏰ لا يوجد تاريخ انتهاء محدد")
                    
            elif credentials.expired and credentials.refresh_token:
                print("⚠️ التوكن منتهي لكن يمكن تجديده")
                
                # محاولة تجديد التوكن
                print("🔄 محاولة تجديد التوكن...")
                if token_manager.refresh_token():
                    print("✅ تم تجديد التوكن بنجاح")
                else:
                    print("❌ فشل في تجديد التوكن")
                    return False
            else:
                print("❌ التوكن غير صالح ولا يمكن تجديده")
                return False
        else:
            print("⚠️ لا يوجد توكن محفوظ - مطلوب مصادقة جديدة")
            
            # إنشاء رابط المصادقة
            auth_url = token_manager.get_authorization_url()
            if auth_url:
                print("✅ تم إنشاء رابط المصادقة بنجاح")
                print(f"   🔗 الرابط: {auth_url[:50]}...")
            else:
                print("❌ فشل في إنشاء رابط المصادقة")
                return False
                
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مدير التوكن: {e}")
        return False

def test_youtube_uploader():
    """اختبار رافع YouTube"""
    print("\n3️⃣ اختبار رافع YouTube...")
    
    try:
        uploader = YouTubeUploader()
        print("✅ تم إنشاء رافع YouTube بنجاح")
        
        # التحقق من خدمة YouTube API
        if uploader.youtube:
            print("✅ تم إعداد خدمة YouTube API بنجاح")
            
            # اختبار الحصول على معلومات القناة
            try:
                channel_info = uploader.get_channel_info()
                if channel_info:
                    print("✅ تم الحصول على معلومات القناة:")
                    print(f"   📺 اسم القناة: {channel_info.get('title', 'غير متوفر')}")
                    print(f"   👥 المشتركون: {channel_info.get('subscriber_count', 'غير متوفر')}")
                    print(f"   🎬 عدد الفيديوهات: {channel_info.get('video_count', 'غير متوفر')}")
                else:
                    print("⚠️ لم يتم العثور على معلومات القناة")
            except Exception as e:
                print(f"⚠️ خطأ في الحصول على معلومات القناة: {e}")
                
        else:
            print("❌ فشل في إعداد خدمة YouTube API")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار رافع YouTube: {e}")
        return False

def test_telegram_bot():
    """اختبار بوت Telegram"""
    print("\n4️⃣ اختبار بوت Telegram...")
    
    try:
        bot = get_bot()
        print("✅ تم إنشاء بوت Telegram بنجاح")
        
        # اختبار إرسال رسالة
        test_message = f"""
🧪 *اختبار نظام YouTube OAuth*

🕐 *الوقت:* {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

✅ جميع المكونات تعمل بشكل صحيح:
• مدير التوكن
• رافع YouTube  
• بوت Telegram

🚀 النظام جاهز للعمل!

#اختبار_ناجح #YouTube #OAuth
        """.strip()
        
        if bot.send_message(test_message):
            print("✅ تم إرسال رسالة اختبار بنجاح")
        else:
            print("⚠️ فشل في إرسال رسالة الاختبار")
            
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار بوت Telegram: {e}")
        return False

def test_config_file():
    """اختبار ملف الإعدادات"""
    print("\n5️⃣ اختبار ملف الإعدادات...")
    
    config_file = "config.toml"
    
    if not os.path.exists(config_file):
        print(f"❌ ملف {config_file} غير موجود")
        return False
        
    try:
        import toml
        with open(config_file, 'r', encoding='utf-8') as f:
            config = toml.load(f)
            
        # التحقق من الأقسام المطلوبة
        required_sections = ['youtube', 'telegram', 'automation']
        missing_sections = [section for section in required_sections 
                          if section not in config]
        
        if missing_sections:
            print(f"❌ أقسام مفقودة في ملف الإعدادات: {missing_sections}")
            return False
            
        print(f"✅ ملف {config_file} صحيح")
        
        # عرض الإعدادات المهمة
        if 'youtube' in config:
            print(f"   🎬 YouTube: {config['youtube'].get('privacy_status', 'غير محدد')}")
            
        if 'telegram' in config:
            bot_token = config['telegram'].get('bot_token', '')
            if bot_token:
                print(f"   📱 Telegram: {bot_token[:20]}...")
            else:
                print("   📱 Telegram: غير محدد")
                
        if 'automation' in config:
            enabled = config['automation'].get('enabled', False)
            print(f"   🤖 الأتمتة: {'مفعلة' if enabled else 'معطلة'}")
            
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قراءة ملف الإعدادات: {e}")
        return False

def run_full_test():
    """تشغيل الاختبار الكامل"""
    print_banner()
    
    tests = [
        ("ملف client_secret.json", test_client_secret_file),
        ("ملف الإعدادات", test_config_file),
        ("مدير التوكن", test_token_manager),
        ("رافع YouTube", test_youtube_uploader),
        ("بوت Telegram", test_telegram_bot),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
            results.append((test_name, False))
    
    # عرض النتائج النهائية
    print("\n" + "="*60)
    print("📊 نتائج الاختبار:")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print("="*60)
    print(f"📈 النتيجة النهائية: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! النظام جاهز للعمل.")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
        return False

def main():
    """الدالة الرئيسية"""
    try:
        success = run_full_test()
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n⚠️ تم إلغاء الاختبار بواسطة المستخدم")
        return 1
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
