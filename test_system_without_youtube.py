#!/usr/bin/env python3
"""
اختبار النظام بدون YouTube للتأكد من عمل باقي المكونات
"""

import sys
import logging
from pathlib import Path

# إعداد السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_reddit_integration():
    """اختبار تكامل Reddit"""
    try:
        print("🔍 اختبار تكامل Reddit...")
        from reddit.subreddit import get_subreddit_threads
        
        # جلب منشور واحد للاختبار
        reddit_object = get_subreddit_threads(None)
        
        if reddit_object and reddit_object.get('thread_title'):
            print(f"✅ Reddit - تم جلب منشور: {reddit_object['thread_title'][:50]}...")
            return True, reddit_object
        else:
            print("❌ Reddit - فشل في جلب المنشور")
            return False, None
            
    except Exception as e:
        print(f"❌ Reddit - خطأ: {e}")
        return False, None

def test_gemini_integration(reddit_object):
    """اختبار تكامل Gemini AI"""
    try:
        print("🔍 اختبار تكامل Gemini AI...")
        from automation.gemini_content_generator import generate_video_content
        
        if not reddit_object:
            print("❌ Gemini - لا يوجد محتوى Reddit للاختبار")
            return False, None
            
        # إنشاء محتوى تجريبي
        content = generate_video_content(
            reddit_object.get('thread_title', 'Test Title'),
            'Test content for video generation',
            'AskReddit'
        )
        
        if content and content.get('title'):
            print(f"✅ Gemini - تم إنشاء عنوان: {content['title'][:50]}...")
            return True, content
        else:
            print("❌ Gemini - فشل في إنشاء المحتوى")
            return False, None
            
    except Exception as e:
        print(f"❌ Gemini - خطأ: {e}")
        return False, None

def test_telegram_integration():
    """اختبار تكامل Telegram"""
    try:
        print("🔍 اختبار تكامل Telegram...")
        from automation.telegram_bot import send_notification
        
        # إرسال رسالة اختبار
        success = send_notification("🧪 اختبار النظام - جميع المكونات تعمل!")
        
        if success:
            print("✅ Telegram - تم إرسال الإشعار بنجاح")
            return True
        else:
            print("❌ Telegram - فشل في إرسال الإشعار")
            return False
            
    except Exception as e:
        print(f"❌ Telegram - خطأ: {e}")
        return False

def test_tts_system(reddit_object):
    """اختبار نظام تحويل النص إلى كلام"""
    try:
        print("🔍 اختبار نظام TTS...")
        from video_creation.voices import save_text_to_mp3
        
        if not reddit_object:
            print("❌ TTS - لا يوجد محتوى للاختبار")
            return False
            
        # اختبار تحويل نص قصير
        test_object = {
            'thread_title': reddit_object.get('thread_title', 'Test'),
            'thread_post': 'Test post content',
            'comments': [
                {'comment_body': 'Test comment 1'},
                {'comment_body': 'Test comment 2'}
            ]
        }
        
        length, num_comments = save_text_to_mp3(test_object)
        
        if length > 0:
            print(f"✅ TTS - تم إنشاء صوت بطول {length:.1f} ثانية")
            return True
        else:
            print("❌ TTS - فشل في إنشاء الصوت")
            return False
            
    except Exception as e:
        print(f"❌ TTS - خطأ: {e}")
        return False

def test_screenshot_system(reddit_object):
    """اختبار نظام لقطات الشاشة"""
    try:
        print("🔍 اختبار نظام لقطات الشاشة...")
        from video_creation.screenshot_downloader import get_screenshots_of_reddit_posts
        
        if not reddit_object:
            print("❌ Screenshots - لا يوجد محتوى للاختبار")
            return False
            
        # اختبار التقاط لقطة واحدة
        get_screenshots_of_reddit_posts(reddit_object, 2)  # لقطتان فقط للاختبار
        
        # فحص وجود الملفات
        assets_dir = Path("assets/temp")
        if assets_dir.exists() and any(assets_dir.glob("*.png")):
            print("✅ Screenshots - تم التقاط لقطات الشاشة")
            return True
        else:
            print("❌ Screenshots - فشل في التقاط لقطات الشاشة")
            return False
            
    except Exception as e:
        print(f"❌ Screenshots - خطأ: {e}")
        return False

def cleanup_test_files():
    """تنظيف ملفات الاختبار"""
    try:
        print("🗑️ تنظيف ملفات الاختبار...")
        
        # حذف الملفات المؤقتة
        temp_dirs = ["assets/temp", "assets/mp3"]
        for temp_dir in temp_dirs:
            temp_path = Path(temp_dir)
            if temp_path.exists():
                import shutil
                shutil.rmtree(temp_path)
                
        print("✅ تم تنظيف ملفات الاختبار")
        
    except Exception as e:
        print(f"⚠️ تحذير في التنظيف: {e}")

def main():
    """الدالة الرئيسية"""
    print("🤖 اختبار النظام بدون YouTube")
    print("="*60)
    
    results = {}
    reddit_object = None
    
    # اختبار Reddit
    reddit_success, reddit_object = test_reddit_integration()
    results['Reddit'] = reddit_success
    
    # اختبار Gemini (يحتاج Reddit object)
    if reddit_success:
        gemini_success, content = test_gemini_integration(reddit_object)
        results['Gemini AI'] = gemini_success
    else:
        results['Gemini AI'] = False
        print("⏭️ تخطي اختبار Gemini - Reddit غير متوفر")
    
    # اختبار Telegram
    telegram_success = test_telegram_integration()
    results['Telegram'] = telegram_success
    
    # اختبار TTS (اختياري - قد يستغرق وقت)
    print("\n⚠️ اختبار TTS قد يستغرق وقتاً... (اضغط Ctrl+C للتخطي)")
    try:
        tts_success = test_tts_system(reddit_object)
        results['TTS'] = tts_success
    except KeyboardInterrupt:
        print("\n⏭️ تم تخطي اختبار TTS")
        results['TTS'] = 'تم التخطي'
    
    # اختبار Screenshots (اختياري)
    try:
        screenshot_success = test_screenshot_system(reddit_object)
        results['Screenshots'] = screenshot_success
    except KeyboardInterrupt:
        print("\n⏭️ تم تخطي اختبار Screenshots")
        results['Screenshots'] = 'تم التخطي'
    
    # عرض النتائج
    print("\n" + "="*60)
    print("📊 ملخص نتائج الاختبار:")
    print("="*60)
    
    all_core_working = True
    core_components = ['Reddit', 'Gemini AI', 'Telegram']
    
    for component, status in results.items():
        if status is True:
            status_icon = "✅"
        elif status is False:
            status_icon = "❌"
            if component in core_components:
                all_core_working = False
        else:
            status_icon = "⏭️"
            
        print(f"{status_icon} {component}: {status}")
    
    print("\n" + "="*60)
    if all_core_working:
        print("🎉 المكونات الأساسية تعمل بشكل صحيح!")
        print("📋 متبقي فقط: إعداد YouTube Service Account")
        print("📖 راجع: YOUTUBE_SERVICE_ACCOUNT_GUIDE.md")
        print("\n🚀 بعد إعداد YouTube، يمكنك تشغيل:")
        print("python run_automated_system.py")
    else:
        print("❌ بعض المكونات الأساسية لا تعمل")
        print("🔧 يرجى إصلاح المشاكل أولاً")
    
    # تنظيف ملفات الاختبار
    cleanup_test_files()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
        cleanup_test_files()
    except Exception as e:
        print(f"\n❌ خطأ عام في الاختبار: {e}")
        cleanup_test_files()
