#!/usr/bin/env python3
"""
اختبار سريع للاتصال بـ Reddit API
للتحقق من صحة بيانات الاعتماد
"""

import praw
import toml
import sys
from pathlib import Path

def test_reddit_connection():
    """اختبار الاتصال بـ Reddit API"""
    try:
        print("🔍 اختبار الاتصال بـ Reddit API...")
        
        # تحميل الإعدادات
        if not Path("config.toml").exists():
            print("❌ ملف config.toml غير موجود")
            return False
            
        config = toml.load("config.toml")
        reddit_creds = config.get("reddit", {}).get("creds", {})
        
        # التحقق من وجود البيانات
        required_fields = ["client_id", "client_secret", "username", "password"]
        missing_fields = []
        
        for field in required_fields:
            if not reddit_creds.get(field) or reddit_creds[field] == f"YOUR_REDDIT_{field.upper()}_HERE":
                missing_fields.append(field)
                
        if missing_fields:
            print(f"❌ بيانات مفقودة: {', '.join(missing_fields)}")
            print("يرجى تحديث config.toml بالبيانات الصحيحة")
            return False
            
        # إنشاء اتصال Reddit
        reddit = praw.Reddit(
            client_id=reddit_creds["client_id"],
            client_secret=reddit_creds["client_secret"],
            username=reddit_creds["username"],
            password=reddit_creds["password"],
            user_agent="RedditVideoMaker/1.0 by Suitable_Reach782"
        )
        
        # اختبار الاتصال
        print("🔐 اختبار المصادقة...")
        user = reddit.user.me()
        print(f"✅ تم الاتصال بنجاح! المستخدم: {user.name}")
        
        # اختبار جلب منشور
        print("📝 اختبار جلب منشورات...")
        subreddit = reddit.subreddit("AskReddit")
        
        # جلب منشور واحد للاختبار
        for submission in subreddit.hot(limit=1):
            print(f"✅ تم جلب منشور: {submission.title[:50]}...")
            
            # اختبار جلب تعليقات
            submission.comments.replace_more(limit=0)
            comment_count = len(submission.comments.list())
            print(f"✅ تم جلب {comment_count} تعليق")
            break
            
        print("🎉 جميع الاختبارات نجحت! Reddit API يعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاتصال بـ Reddit: {e}")
        
        # نصائح لحل المشاكل الشائعة
        error_str = str(e).lower()
        if "401" in error_str or "unauthorized" in error_str:
            print("\n💡 نصائح لحل المشكلة:")
            print("1. تحقق من صحة client_id و client_secret")
            print("2. تحقق من صحة اسم المستخدم وكلمة المرور")
            print("3. تأكد من أن نوع التطبيق 'script'")
            
        elif "403" in error_str or "forbidden" in error_str:
            print("\n💡 نصائح لحل المشكلة:")
            print("1. تأكد من أن التطبيق من نوع 'script'")
            print("2. تحقق من أذونات الحساب")
            
        elif "429" in error_str or "rate limit" in error_str:
            print("\n💡 نصائح لحل المشكلة:")
            print("1. انتظر قليلاً قبل إعادة المحاولة")
            print("2. تم تجاوز حد الطلبات")
            
        return False

def show_current_config():
    """عرض الإعدادات الحالية"""
    try:
        config = toml.load("config.toml")
        reddit_creds = config.get("reddit", {}).get("creds", {})
        
        print("\n📋 الإعدادات الحالية:")
        print("=" * 40)
        print(f"Client ID: {reddit_creds.get('client_id', 'غير محدد')}")
        print(f"Client Secret: {'*' * 20 if reddit_creds.get('client_secret') else 'غير محدد'}")
        print(f"Username: {reddit_creds.get('username', 'غير محدد')}")
        print(f"Password: {'*' * 10 if reddit_creds.get('password') else 'غير محدد'}")
        print("=" * 40)
        
    except Exception as e:
        print(f"خطأ في قراءة الإعدادات: {e}")

def main():
    """الدالة الرئيسية"""
    print("🤖 اختبار Reddit API للأداة")
    print("=" * 50)
    
    # عرض الإعدادات الحالية
    show_current_config()
    
    # اختبار الاتصال
    if test_reddit_connection():
        print("\n🎉 الأداة جاهزة للعمل مع Reddit!")
        print("يمكنك الآن تشغيل النظام الكامل:")
        print("python run_automated_system.py")
    else:
        print("\n❌ يرجى إصلاح مشاكل Reddit API أولاً")
        print("\nالخطوات المطلوبة:")
        print("1. تحديث كلمة المرور في config.toml")
        print("2. التأكد من صحة جميع البيانات")
        print("3. إعادة تشغيل هذا الاختبار")

if __name__ == "__main__":
    main()
