{% extends "base.html" %}

{% block title %}لوحة التحكم - مولد الفيديوهات{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="h3 mb-3">
            <i class="bi bi-speedometer2"></i>
            لوحة التحكم
        </h1>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card stat-card success">
            <div class="card-body text-center">
                <i class="bi bi-camera-video fs-1 mb-2"></i>
                <h4 id="videos-created">0</h4>
                <p class="mb-0">فيديوهات تم إنشاؤها</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card stat-card info">
            <div class="card-body text-center">
                <i class="bi bi-upload fs-1 mb-2"></i>
                <h4 id="videos-published">0</h4>
                <p class="mb-0">فيديوهات تم نشرها</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card stat-card warning">
            <div class="card-body text-center">
                <i class="bi bi-exclamation-triangle fs-1 mb-2"></i>
                <h4 id="errors-count">0</h4>
                <p class="mb-0">أخطاء</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card stat-card">
            <div class="card-body text-center">
                <i class="bi bi-clock fs-1 mb-2"></i>
                <h4 id="uptime">--</h4>
                <p class="mb-0">وقت التشغيل</p>
            </div>
        </div>
    </div>
</div>

<!-- معلومات النظام -->
<div class="row mb-4">
    <div class="col-md-6 mb-3">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-cpu"></i>
                    استخدام الموارد
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">وحدة المعالجة المركزية</label>
                    <div class="progress">
                        <div id="cpu-progress" class="progress-bar" role="progressbar" style="width: 0%">0%</div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">الذاكرة العشوائية</label>
                    <div class="progress">
                        <div id="memory-progress" class="progress-bar bg-info" role="progressbar" style="width: 0%">0%</div>
                    </div>
                    <small class="text-muted" id="memory-details">0 GB / 0 GB</small>
                </div>
                
                <div class="mb-0">
                    <label class="form-label">مساحة القرص</label>
                    <div class="progress">
                        <div id="disk-progress" class="progress-bar bg-warning" role="progressbar" style="width: 0%">0%</div>
                    </div>
                    <small class="text-muted" id="disk-details">0 GB متاح من 0 GB</small>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 mb-3">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-activity"></i>
                    النشاط الحديث
                </h5>
            </div>
            <div class="card-body">
                <div id="recent-activity">
                    <div class="text-center text-muted">
                        <i class="bi bi-hourglass-split"></i>
                        جاري تحميل النشاط...
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- آخر فيديو -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-film"></i>
                    آخر فيديو تم إنشاؤه
                </h5>
            </div>
            <div class="card-body" id="last-video-info">
                <div class="text-center text-muted">
                    <i class="bi bi-camera-video-off"></i>
                    لم يتم إنشاء أي فيديو بعد
                </div>
            </div>
        </div>
    </div>
</div>

<!-- أدوات التحكم -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-gear"></i>
                    أدوات التحكم
                </h5>
            </div>
            <div class="card-body">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-success" onclick="controlSystem('start')">
                        <i class="bi bi-play-fill"></i>
                        تشغيل
                    </button>
                    <button type="button" class="btn btn-warning" onclick="controlSystem('stop')">
                        <i class="bi bi-pause-fill"></i>
                        إيقاف
                    </button>
                    <button type="button" class="btn btn-info" onclick="controlSystem('restart')">
                        <i class="bi bi-arrow-clockwise"></i>
                        إعادة تشغيل
                    </button>
                </div>
                
                <button type="button" class="btn btn-primary ms-3" onclick="refreshData()">
                    <i class="bi bi-arrow-clockwise"></i>
                    تحديث البيانات
                </button>
            </div>
        </div>
    </div>
</div>

<!-- رسائل التنبيه -->
<div id="alert-container"></div>
{% endblock %}

{% block extra_js %}
<script>
    // تحديث البيانات عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        refreshData();
        
        // تحديث تلقائي كل دقيقة
        setInterval(refreshData, 60000);
    });
    
    function refreshData() {
        fetch('/api/stats')
            .then(response => response.json())
            .then(data => {
                updateStats(data);
                updateSystemInfo(data);
                updateLastVideo(data);
            })
            .catch(error => {
                console.error('خطأ في تحديث البيانات:', error);
                showAlert('خطأ في تحديث البيانات', 'danger');
            });
            
        // تحديث النشاط الحديث
        updateRecentActivity();
    }
    
    function updateStats(data) {
        document.getElementById('videos-created').textContent = data.videos_created || 0;
        document.getElementById('videos-published').textContent = data.videos_published || 0;
        document.getElementById('errors-count').textContent = data.errors_count || 0;
        document.getElementById('uptime').textContent = data.uptime || '--';
    }
    
    function updateSystemInfo(data) {
        // وحدة المعالجة المركزية
        const cpuUsage = data.cpu_usage || 0;
        const cpuProgress = document.getElementById('cpu-progress');
        cpuProgress.style.width = cpuUsage + '%';
        cpuProgress.textContent = cpuUsage.toFixed(1) + '%';
        cpuProgress.className = 'progress-bar ' + getProgressBarClass(cpuUsage);
        
        // الذاكرة
        const memoryUsage = data.memory_usage || 0;
        const memoryProgress = document.getElementById('memory-progress');
        memoryProgress.style.width = memoryUsage + '%';
        memoryProgress.textContent = memoryUsage.toFixed(1) + '%';
        
        const memoryDetails = document.getElementById('memory-details');
        memoryDetails.textContent = `${data.memory_used || 0} GB / ${data.memory_total || 0} GB`;
        
        // القرص
        const diskUsage = data.disk_usage || 0;
        const diskProgress = document.getElementById('disk-progress');
        diskProgress.style.width = diskUsage + '%';
        diskProgress.textContent = diskUsage.toFixed(1) + '%';
        
        const diskDetails = document.getElementById('disk-details');
        diskDetails.textContent = `${data.disk_free || 0} GB متاح من ${data.disk_total || 0} GB`;
    }
    
    function updateLastVideo(data) {
        const container = document.getElementById('last-video-info');
        
        if (data.last_video) {
            const video = data.last_video;
            const timestamp = new Date(video.timestamp).toLocaleString('ar-SA');
            
            container.innerHTML = `
                <div class="row">
                    <div class="col-md-8">
                        <h6>معلومات الفيديو:</h6>
                        <p class="mb-1"><strong>الوقت:</strong> ${timestamp}</p>
                        ${video.info ? `<p class="mb-1"><strong>التفاصيل:</strong> ${JSON.stringify(video.info)}</p>` : ''}
                        ${video.published_url ? `<p class="mb-0"><strong>الرابط:</strong> <a href="${video.published_url}" target="_blank">${video.published_url}</a></p>` : ''}
                    </div>
                    <div class="col-md-4 text-end">
                        <span class="badge bg-success fs-6">تم النشر</span>
                    </div>
                </div>
            `;
        } else {
            container.innerHTML = `
                <div class="text-center text-muted">
                    <i class="bi bi-camera-video-off"></i>
                    لم يتم إنشاء أي فيديو بعد
                </div>
            `;
        }
    }
    
    function updateRecentActivity() {
        fetch('/api/logs?limit=5')
            .then(response => response.json())
            .then(logs => {
                const container = document.getElementById('recent-activity');
                
                if (logs.length > 0) {
                    container.innerHTML = logs.map(log => `
                        <div class="log-entry ${log.level.toLowerCase()}">
                            <small class="text-muted">${log.timestamp}</small><br>
                            ${log.message}
                        </div>
                    `).join('');
                } else {
                    container.innerHTML = `
                        <div class="text-center text-muted">
                            <i class="bi bi-journal-x"></i>
                            لا توجد أنشطة حديثة
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('خطأ في تحديث النشاط:', error);
            });
    }
    
    function controlSystem(action) {
        fetch(`/api/control/${action}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert(data.message, 'success');
                    setTimeout(refreshData, 2000); // تحديث البيانات بعد ثانيتين
                } else {
                    showAlert(data.error || 'حدث خطأ غير معروف', 'danger');
                }
            })
            .catch(error => {
                console.error('خطأ في التحكم بالنظام:', error);
                showAlert('خطأ في الاتصال بالخادم', 'danger');
            });
    }
    
    function getProgressBarClass(percentage) {
        if (percentage < 50) return 'bg-success';
        if (percentage < 80) return 'bg-warning';
        return 'bg-danger';
    }
    
    function showAlert(message, type) {
        const container = document.getElementById('alert-container');
        const alertId = 'alert-' + Date.now();
        
        const alertHtml = `
            <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        container.innerHTML = alertHtml;
        
        // إزالة التنبيه تلقائياً بعد 5 ثوان
        setTimeout(() => {
            const alert = document.getElementById(alertId);
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }
</script>
{% endblock %}
