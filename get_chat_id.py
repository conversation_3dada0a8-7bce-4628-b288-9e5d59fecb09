#!/usr/bin/env python3
"""
أداة بسيطة للحصول على معرف المحادثة من التيليجرام
"""

import requests
import json
import time

def get_chat_id():
    """الحصول على معرف المحادثة من التيليجرام"""
    
    # قراءة رمز البوت من الإعدادات
    try:
        import toml
        with open("config.toml", "r", encoding="utf-8") as f:
            config = toml.load(f)
        bot_token = config["telegram"]["bot_token"]
    except Exception as e:
        print(f"❌ خطأ في قراءة رمز البوت: {e}")
        return None
    
    print(f"""
🤖 أداة الحصول على معرف المحادثة
================================

رمز البوت: {bot_token[:10]}...

📋 الخطوات:
1. ابحث عن البوت في التيليجرام: @sah8fqwuhfu_bot
2. أرسل /start أو أي رسالة للبوت
3. اضغط Enter هنا للمتابعة...
    """)
    
    input("اضغط Enter بعد إرسال رسالة للبوت...")
    
    # الحصول على التحديثات
    try:
        url = f"https://api.telegram.org/bot{bot_token}/getUpdates"
        print("🔍 البحث عن الرسائل...")
        
        response = requests.get(url, timeout=30)
        
        if response.status_code != 200:
            print(f"❌ خطأ في الاتصال: {response.status_code}")
            return None
            
        data = response.json()
        
        if not data.get('ok'):
            print(f"❌ خطأ من التيليجرام: {data.get('description', 'خطأ غير معروف')}")
            return None
            
        updates = data.get('result', [])
        
        if not updates:
            print("❌ لم يتم العثور على رسائل")
            print("تأكد من إرسال رسالة للبوت أولاً")
            return None
            
        # البحث عن آخر رسالة
        for update in reversed(updates):
            if 'message' in update:
                chat_id = str(update['message']['chat']['id'])
                chat_type = update['message']['chat']['type']
                
                if 'first_name' in update['message']['chat']:
                    name = update['message']['chat']['first_name']
                else:
                    name = update['message']['chat'].get('title', 'غير معروف')
                
                print(f"""
✅ تم العثور على معرف المحادثة!

معرف المحادثة: {chat_id}
نوع المحادثة: {chat_type}
الاسم: {name}
                """)
                
                # حفظ معرف المحادثة
                try:
                    with open("telegram_chat_id.txt", "w") as f:
                        f.write(chat_id)
                    print("✅ تم حفظ معرف المحادثة في telegram_chat_id.txt")
                    
                    # تحديث الإعدادات أيضاً
                    config["telegram"]["chat_id"] = chat_id
                    with open("config.toml", "w", encoding="utf-8") as f:
                        toml.dump(config, f)
                    print("✅ تم تحديث الإعدادات")
                    
                except Exception as e:
                    print(f"⚠️ تم العثور على المعرف لكن فشل في الحفظ: {e}")
                
                return chat_id
        
        print("❌ لم يتم العثور على رسائل صالحة")
        return None
        
    except requests.exceptions.Timeout:
        print("❌ انتهت مهلة الاتصال")
        print("تحقق من اتصال الإنترنت")
        return None
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return None

def test_chat_id(bot_token, chat_id):
    """اختبار معرف المحادثة"""
    try:
        url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
        data = {
            'chat_id': chat_id,
            'text': '🎉 تم إعداد معرف المحادثة بنجاح!'
        }
        
        response = requests.post(url, data=data, timeout=30)
        
        if response.status_code == 200:
            print("✅ تم إرسال رسالة اختبار بنجاح!")
            return True
        else:
            print(f"❌ فشل في إرسال رسالة الاختبار: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار معرف المحادثة: {e}")
        return False

if __name__ == "__main__":
    chat_id = get_chat_id()
    
    if chat_id:
        print("\n🧪 اختبار معرف المحادثة...")
        
        # قراءة رمز البوت مرة أخرى
        try:
            import toml
            with open("config.toml", "r", encoding="utf-8") as f:
                config = toml.load(f)
            bot_token = config["telegram"]["bot_token"]
            
            if test_chat_id(bot_token, chat_id):
                print(f"""
🎉 تم الإعداد بنجاح!

يمكنك الآن تشغيل البوت:
python main.py

معرف المحادثة: {chat_id}
                """)
            else:
                print("⚠️ تم العثور على المعرف لكن فشل في الاختبار")
                
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {e}")
    else:
        print("""
❌ فشل في الحصول على معرف المحادثة

تأكد من:
1. إرسال رسالة للبوت أولاً
2. اتصال الإنترنت
3. صحة رمز البوت
        """)
