#!/usr/bin/env python3
"""
إنشاء فيديو خلفية بسيط لتجنب مشاكل التحميل
"""

import os
import json
from pathlib import Path

def create_simple_background():
    """إنشاء فيديو خلفية بسيط"""
    
    # إنشاء مجلد الفيديو
    video_dir = Path("assets/backgrounds/video")
    video_dir.mkdir(parents=True, exist_ok=True)
    
    # إنشاء فيديو بسيط باستخدام ffmpeg
    simple_file = video_dir / "simple-background.mp4"
    
    try:
        # إنشاء فيديو بسيط بخلفية متدرجة
        import subprocess
        cmd = [
            "ffmpeg", "-y",  # -y للكتابة فوق الملف الموجود
            "-f", "lavfi",   # استخدام lavfi
            "-i", "color=c=blue:size=1080x1920:duration=600",  # خلفية زرقاء
            "-c:v", "libx264",  # ترميز H.264
            "-pix_fmt", "yuv420p",  # تنسيق البكسل
            "-r", "30",      # معدل الإطارات
            str(simple_file)
        ]
        
        print("🔄 إنشاء فيديو خلفية بسيط...")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ تم إنشاء فيديو خلفية بسيط: {simple_file}")
            return True
        else:
            print(f"❌ خطأ في ffmpeg: {result.stderr}")
            return False
            
    except FileNotFoundError:
        print("❌ ffmpeg غير مثبت")
        
        # طريقة بديلة باستخدام PIL
        try:
            from PIL import Image, ImageDraw
            import imageio
            import numpy as np
            
            print("🔄 إنشاء فيديو بديل باستخدام Python...")
            
            # إعدادات الفيديو
            width, height = 1080, 1920
            fps = 30
            duration = 10  # 10 ثوان فقط
            total_frames = fps * duration
            
            frames = []
            
            for frame_num in range(total_frames):
                # إنشاء إطار بخلفية متدرجة
                img = Image.new('RGB', (width, height))
                draw = ImageDraw.Draw(img)
                
                # تدرج لوني بسيط
                for y in range(height):
                    color_value = int(50 + (y / height) * 100)
                    color = (color_value, color_value + 20, color_value + 50)
                    draw.line([(0, y), (width, y)], fill=color)
                
                # إضافة نص بسيط
                draw.text((width//2 - 100, height//2), "Background Video", 
                         fill=(255, 255, 255))
                
                # تحويل إلى numpy array
                frame = np.array(img)
                frames.append(frame)
            
            # حفظ كفيديو
            imageio.mimsave(str(simple_file), frames, fps=fps)
            print(f"✅ تم إنشاء فيديو بديل: {simple_file}")
            return True
            
        except ImportError:
            print("❌ المكتبات المطلوبة غير مثبتة")
            
            # إنشاء ملف فيديو وهمي صغير
            dummy_file = video_dir / "dummy-background.mp4"
            with open(dummy_file, 'wb') as f:
                # كتابة header MP4 بسيط (لن يعمل لكن سيتجنب الخطأ)
                f.write(b'\x00\x00\x00\x20ftypmp42')
                f.write(b'\x00' * 1000)
            
            print(f"⚠️ تم إنشاء ملف وهمي: {dummy_file}")
            print("💡 يُنصح بتثبيت ffmpeg للحصول على فيديو صحيح")
            return True

def update_background_video_config():
    """تحديث إعدادات الفيديو لاستخدام الفيديو البسيط"""
    
    import json
    from pathlib import Path
    
    # تحديث ملف background_videos.json
    video_json_path = Path("utils/background_videos.json")
    
    if video_json_path.exists():
        with open(video_json_path, 'r', encoding='utf-8') as f:
            videos = json.load(f)
    else:
        videos = {"__comment": "Supported Backgrounds. Can add/remove background video here..."}
    
    # إضافة الفيديو البسيط
    videos["simple"] = [
        "local_file",
        "simple-background.mp4",
        "Local Simple",
        "center"
    ]
    
    # حفظ التحديث
    with open(video_json_path, 'w', encoding='utf-8') as f:
        json.dump(videos, f, indent=4, ensure_ascii=False)
    
    # تحديث config.toml
    import toml
    config_path = Path("config.toml")
    
    if config_path.exists():
        with open(config_path, 'r', encoding='utf-8') as f:
            config = toml.load(f)
        
        config["settings"]["background"]["background_video"] = "simple"
        
        with open(config_path, 'w', encoding='utf-8') as f:
            toml.dump(config, f)
        
        print("✅ تم تحديث الإعدادات لاستخدام الفيديو البسيط")

def install_video_packages():
    """تثبيت المكتبات المطلوبة للفيديو"""
    import subprocess
    import sys
    
    packages = ["pillow", "imageio", "numpy"]
    
    for package in packages:
        try:
            if package == "pillow":
                __import__("PIL")
            else:
                __import__(package)
            print(f"✅ {package} مثبت بالفعل")
        except ImportError:
            print(f"🔄 تثبيت {package}...")
            try:
                subprocess.run([sys.executable, "-m", "pip", "install", package], 
                             check=True, capture_output=True)
                print(f"✅ تم تثبيت {package}")
            except subprocess.CalledProcessError:
                print(f"❌ فشل في تثبيت {package}")

if __name__ == "__main__":
    print("🎬 إنشاء فيديو خلفية بسيط")
    print("=" * 35)
    
    # محاولة تثبيت المكتبات المطلوبة
    install_video_packages()
    
    # إنشاء الفيديو البسيط
    if create_simple_background():
        update_background_video_config()
        print("\n🎉 تم! يمكنك الآن تشغيل البوت بدون مشاكل تحميل الفيديو")
        print("💡 الفيديو بسيط جداً، يمكنك استبداله لاحقاً بفيديو أفضل")
    else:
        print("\n❌ فشل في إنشاء الفيديو البسيط")
        print("💡 جرب تثبيت ffmpeg أو استخدم فيديو موجود")
