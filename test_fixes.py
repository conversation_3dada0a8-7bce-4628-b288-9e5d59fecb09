#!/usr/bin/env python3
"""
اختبار الإصلاحات المطبقة على النظام
"""

import os
import sys
import logging
import tempfile
from pathlib import Path

# إعداد المسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# إعداد logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_progress_ffmpeg_zero_division():
    """اختبار إصلاح خطأ القسمة على صفر في ProgressFfmpeg"""
    try:
        from video_creation.final_video import ProgressFfmpeg
        
        print("🔍 اختبار ProgressFfmpeg مع طول فيديو = 0...")
        
        def dummy_callback(progress):
            print(f"Progress: {progress:.2%}")
        
        # اختبار مع طول فيديو = 0
        progress = ProgressFfmpeg(0, dummy_callback)
        
        # محاكاة تشغيل الـ thread لفترة قصيرة
        progress.start()
        import time
        time.sleep(2)
        progress.stop()
        progress.join()
        
        print("✅ ProgressFfmpeg - لا يوجد خطأ قسمة على صفر")
        return True
        
    except Exception as e:
        print(f"❌ ProgressFfmpeg - خطأ: {e}")
        return False

def test_telegram_message_cleaning():
    """اختبار تنظيف رسائل Telegram"""
    try:
        from automation.telegram_bot import EnhancedTelegramBot

        print("🔍 اختبار تنظيف رسائل Telegram...")

        # إنشاء instance مؤقت
        bot = EnhancedTelegramBot()
        
        # رسائل اختبار مع رموز مشكلة
        test_messages = [
            "Test message with *bold* and _italic_",
            "Message with [brackets] and (parentheses)",
            "Special chars: #hashtag @mention",
            "Path: C:\\Users\\<USER>\\file.txt",
            "Markdown: **bold** __italic__ `code`"
        ]
        
        for msg in test_messages:
            cleaned = bot._clean_message_for_telegram(msg)
            print(f"Original: {msg}")
            print(f"Cleaned:  {cleaned}")
            print()
        
        print("✅ Telegram message cleaning - يعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"❌ Telegram message cleaning - خطأ: {e}")
        return False

def test_audio_length_calculation():
    """اختبار حساب طول الملفات الصوتية"""
    try:
        from TTS.engine_wrapper import TTSEngine
        
        print("🔍 اختبار حساب طول الملفات الصوتية...")
        
        # إنشاء ملف صوتي وهمي للاختبار
        test_dir = "assets/temp/test_audio"
        Path(test_dir).mkdir(parents=True, exist_ok=True)
        
        # محاكاة reddit object
        reddit_obj = {
            'thread_id': 'test123',
            'thread_title': 'Test Title',
            'thread_post': 'Test post content',
            'comments': [
                {'comment_body': 'Test comment 1'},
                {'comment_body': 'Test comment 2'}
            ]
        }
        
        # اختبار مع محرك TTS وهمي
        class DummyTTS:
            max_chars = 500
            def run(self, text, filepath, random_voice=False):
                # إنشاء ملف صوتي وهمي
                with open(filepath, 'wb') as f:
                    f.write(b'dummy audio data')
        
        engine = TTSEngine(DummyTTS(), reddit_obj, path=test_dir, max_length=50)
        
        # اختبار call_tts مع ملف غير موجود
        try:
            engine.call_tts("test", "Test text")
            print(f"Length after call_tts: {engine.length}")
        except Exception as e:
            print(f"Expected error in call_tts: {e}")
        
        # تنظيف
        import shutil
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)
        
        print("✅ Audio length calculation - تم الاختبار")
        return True
        
    except Exception as e:
        print(f"❌ Audio length calculation - خطأ: {e}")
        return False

def test_elevenlabs_error_handling():
    """اختبار معالجة أخطاء ElevenLabs"""
    try:
        from TTS.elevenlabs import ElevenLabsTTS

        print("🔍 اختبار معالجة أخطاء ElevenLabs...")

        # إنشاء instance مع مفاتيح وهمية
        elevenlabs = ElevenLabsTTS()
        elevenlabs.api_keys = ["fake_key_1", "fake_key_2"]

        # اختبار مع نص قصير وملف مؤقت
        tmp_file_path = "test_audio_temp.mp3"
        try:
            result = elevenlabs.run("Test text", tmp_file_path)
            print(f"ElevenLabs result: {result}")
        except Exception as e:
            print(f"Expected ElevenLabs error: {e}")
        finally:
            if os.path.exists(tmp_file_path):
                try:
                    os.unlink(tmp_file_path)
                except:
                    pass
        
        print("✅ ElevenLabs error handling - تم الاختبار")
        return True
        
    except Exception as e:
        print(f"❌ ElevenLabs error handling - خطأ: {e}")
        return False

def main():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار الإصلاحات...")
    print("=" * 50)
    
    tests = [
        ("ProgressFfmpeg Zero Division Fix", test_progress_ffmpeg_zero_division),
        ("Telegram Message Cleaning", test_telegram_message_cleaning),
        ("Audio Length Calculation", test_audio_length_calculation),
        ("ElevenLabs Error Handling", test_elevenlabs_error_handling),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} - خطأ غير متوقع: {e}")
            results.append((test_name, False))
    
    # تلخيص النتائج
    print("\n" + "=" * 50)
    print("📊 ملخص نتائج الاختبارات:")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 النتيجة النهائية: {passed}/{len(results)} اختبارات نجحت")
    
    if passed == len(results):
        print("🎉 جميع الإصلاحات تعمل بشكل صحيح!")
    else:
        print("⚠️ بعض الإصلاحات تحتاج مراجعة إضافية")

if __name__ == "__main__":
    main()
