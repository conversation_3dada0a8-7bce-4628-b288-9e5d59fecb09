#!/usr/bin/env python3
"""
إصلاح سريع لمشكلة إنشاء الفيديو
يحل المشاكل الشائعة تلقائياً
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# إعداد السجلات
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_required_directories():
    """إنشاء المجلدات المطلوبة"""
    print("📁 إنشاء المجلدات المطلوبة...")
    
    directories = [
        "results",
        "results/AskReddit",
        "results/AskReddit/OnlyTTS",
        "assets",
        "assets/temp",
        "assets/backgrounds",
        "logs",
        "video_creation/data"
    ]
    
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            print(f"✅ {directory}")
        except Exception as e:
            print(f"❌ فشل في إنشاء {directory}: {e}")

def fix_video_creation_data():
    """إصلاح ملف بيانات الفيديوهات"""
    print("\n📄 إصلاح ملف بيانات الفيديوهات...")
    
    data_file = "video_creation/data/videos.json"
    
    if not os.path.exists(data_file):
        try:
            import json
            with open(data_file, 'w', encoding='utf-8') as f:
                json.dump([], f, ensure_ascii=False, indent=2)
            print(f"✅ تم إنشاء {data_file}")
        except Exception as e:
            print(f"❌ فشل في إنشاء {data_file}: {e}")
    else:
        print(f"✅ {data_file} موجود")

def check_ffmpeg():
    """فحص وإصلاح FFmpeg"""
    print("\n🎬 فحص FFmpeg...")
    
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ FFmpeg يعمل بشكل صحيح")
            return True
    except FileNotFoundError:
        print("❌ FFmpeg غير مثبت")
    except Exception as e:
        print(f"❌ خطأ في FFmpeg: {e}")
    
    # محاولة تثبيت FFmpeg
    print("🔧 محاولة تثبيت FFmpeg...")
    try:
        from utils.ffmpeg_install import ffmpeg_install
        ffmpeg_install()
        print("✅ تم تثبيت FFmpeg")
        return True
    except Exception as e:
        print(f"❌ فشل في تثبيت FFmpeg: {e}")
        return False

def fix_permissions():
    """إصلاح صلاحيات الملفات"""
    print("\n🔐 فحص صلاحيات الملفات...")
    
    # التأكد من إمكانية الكتابة في مجلد results
    test_file = "results/test_write.txt"
    try:
        with open(test_file, 'w') as f:
            f.write("test")
        os.remove(test_file)
        print("✅ صلاحيات الكتابة في مجلد results")
        return True
    except Exception as e:
        print(f"❌ مشكلة في صلاحيات الكتابة: {e}")
        return False

def check_dependencies():
    """فحص المكتبات المطلوبة"""
    print("\n📦 فحص المكتبات المطلوبة...")
    
    required_packages = [
        'ffmpeg-python',
        'praw',
        'requests',
        'rich',
        'toml',
        'PIL',
        'google-generativeai'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'PIL':
                import PIL
            elif package == 'ffmpeg-python':
                import ffmpeg
            elif package == 'google-generativeai':
                import google.generativeai
            else:
                __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} غير مثبت")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n🔧 تثبيت المكتبات المفقودة: {', '.join(missing_packages)}")
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install'] + missing_packages, 
                         check=True)
            print("✅ تم تثبيت المكتبات المفقودة")
        except Exception as e:
            print(f"❌ فشل في تثبيت المكتبات: {e}")
            return False
    
    return True

def fix_config_file():
    """فحص وإصلاح ملف الإعدادات"""
    print("\n⚙️ فحص ملف الإعدادات...")
    
    if not os.path.exists("config.toml"):
        print("❌ ملف config.toml غير موجود")
        print("🔧 يرجى تشغيل python main.py لإنشاء ملف الإعدادات")
        return False
    
    try:
        import toml
        with open("config.toml", 'r', encoding='utf-8') as f:
            config = toml.load(f)
        
        # التحقق من الأقسام المهمة
        required_sections = {
            'settings': {
                'resolution_w': 1080,
                'resolution_h': 1920,
                'opacity': 0.9
            },
            'settings.background': {
                'background_video': 'minecraft',
                'background_audio': 'lofi',
                'background_audio_volume': 0.0
            }
        }
        
        modified = False
        for section_path, defaults in required_sections.items():
            keys = section_path.split('.')
            current = config
            
            # التنقل إلى القسم
            for key in keys[:-1]:
                if key not in current:
                    current[key] = {}
                current = current[key]
            
            # التحقق من القيم
            section_name = keys[-1]
            if section_name not in current:
                current[section_name] = {}
            
            for key, default_value in defaults.items():
                if key not in current[section_name]:
                    current[section_name][key] = default_value
                    modified = True
                    print(f"🔧 إضافة {section_path}.{key} = {default_value}")
        
        if modified:
            with open("config.toml", 'w', encoding='utf-8') as f:
                toml.dump(config, f)
            print("✅ تم تحديث ملف الإعدادات")
        else:
            print("✅ ملف الإعدادات سليم")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص ملف الإعدادات: {e}")
        return False

def run_quick_fix():
    """تشغيل الإصلاح السريع"""
    print("🔧 بدء الإصلاح السريع لمشكلة إنشاء الفيديو")
    print("=" * 60)
    
    fixes = [
        ("إنشاء المجلدات المطلوبة", create_required_directories),
        ("إصلاح ملف بيانات الفيديوهات", fix_video_creation_data),
        ("فحص FFmpeg", check_ffmpeg),
        ("فحص صلاحيات الملفات", fix_permissions),
        ("فحص المكتبات المطلوبة", check_dependencies),
        ("فحص ملف الإعدادات", fix_config_file),
    ]
    
    success_count = 0
    
    for fix_name, fix_func in fixes:
        try:
            if fix_func():
                success_count += 1
        except Exception as e:
            print(f"❌ خطأ في {fix_name}: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 تم إكمال {success_count}/{len(fixes)} إصلاحات بنجاح")
    
    if success_count == len(fixes):
        print("🎉 تم إصلاح جميع المشاكل! يمكنك الآن تشغيل البوت")
        print("\nللاختبار:")
        print("python test_video_creation_fix.py")
        print("\nلتشغيل البوت:")
        print("python main.py")
    else:
        print("⚠️ بعض المشاكل لم يتم إصلاحها. يرجى مراجعة الأخطاء أعلاه")

def show_troubleshooting_tips():
    """عرض نصائح استكشاف الأخطاء"""
    print("\n" + "=" * 60)
    print("💡 نصائح استكشاف الأخطاء")
    print("=" * 60)
    
    print("""
إذا استمرت المشكلة، جرب ما يلي:

1. تأكد من أن Python 3.7+ مثبت:
   python --version

2. تأكد من تثبيت جميع المكتبات:
   pip install -r requirements.txt

3. تأكد من تثبيت Playwright:
   python -m playwright install

4. تحقق من مساحة القرص الصلب:
   - يجب أن تكون هناك مساحة كافية في مجلد results

5. تحقق من صلاحيات المجلدات:
   - تأكد من إمكانية الكتابة في مجلد المشروع

6. إذا كان هناك خطأ في FFmpeg:
   - جرب إعادة تثبيت FFmpeg
   - تأكد من إضافة FFmpeg إلى PATH

7. للحصول على مساعدة إضافية:
   - راجع ملف README_ARABIC.md
   - تحقق من ملفات السجلات في مجلد logs/
    """)

if __name__ == "__main__":
    try:
        run_quick_fix()
        show_troubleshooting_tips()
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الإصلاح بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ حرج في الإصلاح: {e}")
        import traceback
        print(f"تفاصيل الخطأ:\n{traceback.format_exc()}")
