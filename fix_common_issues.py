#!/usr/bin/env python3
"""
إصلاح المشاكل الشائعة في Reddit Video Maker Bot
"""

import os
import sys
import subprocess
from pathlib import Path

def fix_telegram_chat_id():
    """إصلاح مشكلة معرف محادثة Telegram"""
    chat_id_file = "telegram_chat_id.txt"
    if not os.path.exists(chat_id_file):
        with open(chat_id_file, 'w') as f:
            f.write("YOUR_CHAT_ID_HERE")
        print("✅ تم إنشاء ملف telegram_chat_id.txt")
    else:
        print("✅ ملف telegram_chat_id.txt موجود")

def update_dependencies():
    """تحديث المكتبات المطلوبة"""
    print("🔄 تحديث المكتبات...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "praw", "playwright"], check=True)
        print("✅ تم تحديث المكتبات بنجاح")
    except subprocess.CalledProcessError:
        print("❌ فشل في تحديث المكتبات")

def install_playwright_browsers():
    """تثبيت متصفحات Playwright"""
    print("🔄 تثبيت متصفحات Playwright...")
    try:
        subprocess.run([sys.executable, "-m", "playwright", "install"], check=True)
        print("✅ تم تثبيت متصفحات Playwright بنجاح")
    except subprocess.CalledProcessError:
        print("❌ فشل في تثبيت متصفحات Playwright")

def create_temp_directories():
    """إنشاء المجلدات المطلوبة"""
    directories = [
        "assets/temp",
        "logs",
        "assets/backgrounds"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ تم إنشاء مجلد: {directory}")

def check_ffmpeg():
    """فحص وجود FFmpeg"""
    try:
        subprocess.run(["ffmpeg", "-version"], capture_output=True, check=True)
        print("✅ FFmpeg مثبت ويعمل بشكل صحيح")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ FFmpeg غير مثبت أو لا يعمل")
        print("💡 يرجى تثبيت FFmpeg من: https://ffmpeg.org/download.html")

def main():
    """تشغيل جميع الإصلاحات"""
    print("🔧 بدء إصلاح المشاكل الشائعة...")
    print("=" * 50)
    
    fix_telegram_chat_id()
    create_temp_directories()
    update_dependencies()
    install_playwright_browsers()
    check_ffmpeg()
    
    print("=" * 50)
    print("✅ تم الانتهاء من الإصلاحات!")
    print("💡 يمكنك الآن تشغيل البرنامج بـ: python main.py")

if __name__ == "__main__":
    main()
