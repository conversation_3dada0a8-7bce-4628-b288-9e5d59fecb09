#!/usr/bin/env python3
"""
إعداد سريع لنظام YouTube OAuth
يقوم بجميع خطوات الإعداد تلقائياً
"""

import os
import sys
import json
import subprocess
from pathlib import Path

def print_banner():
    """طباعة شعار الإعداد السريع"""
    print("""
╔══════════════════════════════════════════════════════════════╗
║                    🚀 الإعداد السريع                         ║
║                YouTube OAuth + Telegram Bot                 ║
║                   Reddit Video Maker Bot                    ║
╚══════════════════════════════════════════════════════════════╝
    """)

def check_requirements():
    """التحقق من المتطلبات"""
    print("1️⃣ التحقق من المتطلبات...")
    
    # التحقق من Python
    if sys.version_info < (3, 7):
        print("❌ يتطلب Python 3.7 أو أحدث")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}")
    
    # التحقق من pip
    try:
        import pip
        print("✅ pip متوفر")
    except ImportError:
        print("❌ pip غير متوفر")
        return False
    
    return True

def install_dependencies():
    """تثبيت المكتبات المطلوبة"""
    print("\n2️⃣ تثبيت المكتبات المطلوبة...")
    
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], check=True, capture_output=True)
        print("✅ تم تثبيت جميع المكتبات بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت المكتبات: {e}")
        return False
    except FileNotFoundError:
        print("❌ ملف requirements.txt غير موجود")
        return False

def check_client_secret():
    """التحقق من ملف client_secret.json"""
    print("\n3️⃣ التحقق من ملف client_secret.json...")
    
    client_secret_file = "client_secret.json"
    
    if not os.path.exists(client_secret_file):
        print(f"❌ ملف {client_secret_file} غير موجود!")
        print("\n📋 خطوات الحصول على ملف client_secret.json:")
        print("1. اذهب إلى: https://console.cloud.google.com/")
        print("2. إنشاء مشروع جديد أو اختيار مشروع موجود")
        print("3. تفعيل YouTube Data API v3")
        print("4. إنشاء OAuth 2.0 Client ID (Desktop Application)")
        print("5. تحميل ملف JSON وإعادة تسميته إلى client_secret.json")
        print("6. وضع الملف في المجلد الرئيسي للبرنامج")
        print("\n💡 راجع ملف YOUTUBE_OAUTH_SETUP_GUIDE.md للتفاصيل")
        return False
        
    try:
        with open(client_secret_file, 'r') as f:
            data = json.load(f)
            
        if 'installed' in data and 'client_id' in data['installed']:
            print(f"✅ ملف {client_secret_file} صحيح")
            return True
        else:
            print(f"❌ بنية ملف {client_secret_file} غير صحيحة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في قراءة ملف {client_secret_file}: {e}")
        return False

def setup_telegram():
    """إعداد بوت Telegram"""
    print("\n4️⃣ إعداد بوت Telegram...")
    
    try:
        # إضافة مجلد automation للمسار
        sys.path.append(str(Path(__file__).parent / "automation"))
        from automation.telegram_bot import get_bot
        
        bot = get_bot()
        
        # محاولة إرسال رسالة اختبار
        test_message = """
🚀 *بدء إعداد نظام YouTube OAuth*

🔧 جاري إعداد النظام...
📱 تم تفعيل إشعارات Telegram بنجاح!

⏳ انتظر إكمال الإعداد...

#إعداد_النظام #YouTube #OAuth
        """.strip()
        
        if bot.send_message(test_message):
            print("✅ تم إعداد بوت Telegram بنجاح")
            return True
        else:
            print("⚠️ بوت Telegram يعمل لكن لم يتم إرسال رسالة الاختبار")
            print("💡 تأكد من إرسال رسالة للبوت أولاً لتفعيل المحادثة")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في إعداد بوت Telegram: {e}")
        print("💡 تحقق من إعدادات Telegram في ملف config.toml")
        return False

def run_oauth_setup():
    """تشغيل إعداد OAuth"""
    print("\n5️⃣ إعداد مصادقة YouTube OAuth...")
    
    try:
        # تشغيل سكريبت الإعداد
        result = subprocess.run([
            sys.executable, "setup_youtube_auth.py"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ تم إعداد OAuth بنجاح")
            return True
        else:
            print("⚠️ يتطلب إعداد OAuth يدوي")
            print("\n📋 خطوات الإعداد اليدوي:")
            print("1. شغل الأمر: python setup_youtube_auth.py")
            print("2. اتبع الرابط الذي سيظهر")
            print("3. سجل دخولك بحساب Google")
            print("4. انسخ الكود من الرابط النهائي")
            print("5. شغل: python setup_youtube_auth.py --code YOUR_CODE")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في إعداد OAuth: {e}")
        return False

def run_system_test():
    """تشغيل اختبار النظام"""
    print("\n6️⃣ اختبار النظام...")
    
    try:
        result = subprocess.run([
            sys.executable, "test_youtube_oauth.py"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ جميع اختبارات النظام نجحت")
            return True
        else:
            print("⚠️ بعض اختبارات النظام فشلت")
            print("💡 شغل الأمر التالي لمزيد من التفاصيل:")
            print("   python test_youtube_oauth.py")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار النظام: {e}")
        return False

def send_completion_notification():
    """إرسال إشعار إكمال الإعداد"""
    try:
        sys.path.append(str(Path(__file__).parent / "automation"))
        from automation.telegram_bot import get_bot
        from datetime import datetime
        
        bot = get_bot()
        
        message = f"""
🎉 *تم إكمال إعداد نظام YouTube OAuth بنجاح!*

✅ *المكونات المُعدة:*
• مصادقة YouTube OAuth
• إدارة التوكن التلقائية
• إشعارات Telegram
• مراقبة النظام

🚀 *النظام جاهز للعمل!*

📋 *الأوامر المفيدة:*
• تشغيل النظام: `python run_automated_system.py`
• مراقبة التوكن: `python monitor_youtube_token.py --monitor`
• اختبار النظام: `python test_youtube_oauth.py`

⏰ *وقت الإكمال:* {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

#إعداد_مكتمل #YouTube #OAuth #جاهز
        """.strip()
        
        bot.send_message(message)
        print("✅ تم إرسال إشعار إكمال الإعداد")
        
    except Exception as e:
        print(f"⚠️ فشل في إرسال إشعار الإكمال: {e}")

def main():
    """الدالة الرئيسية"""
    print_banner()
    
    steps = [
        ("التحقق من المتطلبات", check_requirements),
        ("تثبيت المكتبات", install_dependencies),
        ("التحقق من client_secret.json", check_client_secret),
        ("إعداد Telegram", setup_telegram),
        ("إعداد OAuth", run_oauth_setup),
        ("اختبار النظام", run_system_test),
    ]
    
    completed_steps = 0
    total_steps = len(steps)
    
    for step_name, step_func in steps:
        try:
            if step_func():
                completed_steps += 1
            else:
                print(f"\n❌ فشل في: {step_name}")
                break
        except Exception as e:
            print(f"\n❌ خطأ في {step_name}: {e}")
            break
    
    # عرض النتائج النهائية
    print("\n" + "="*60)
    print("📊 نتائج الإعداد:")
    print("="*60)
    print(f"📈 تم إكمال: {completed_steps}/{total_steps} خطوة")
    
    if completed_steps == total_steps:
        print("🎉 تم إكمال الإعداد بنجاح!")
        print("🚀 النظام جاهز للعمل")
        
        # إرسال إشعار الإكمال
        send_completion_notification()
        
        print("\n📋 الخطوات التالية:")
        print("1. شغل النظام التلقائي: python run_automated_system.py")
        print("2. راقب التوكن: python monitor_youtube_token.py --monitor")
        print("3. راجع الدليل: YOUTUBE_OAUTH_SETUP_GUIDE.md")
        
        return 0
    else:
        print("⚠️ لم يكتمل الإعداد بالكامل")
        print("💡 راجع الأخطاء أعلاه وأعد المحاولة")
        print("📖 راجع الدليل: YOUTUBE_OAUTH_SETUP_GUIDE.md")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️ تم إلغاء الإعداد بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
