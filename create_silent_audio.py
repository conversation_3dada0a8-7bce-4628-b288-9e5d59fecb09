#!/usr/bin/env python3
"""
إنشاء ملف صوتي صامت لتجنب مشاكل التحميل
"""

import os
from pathlib import Path

def create_silent_audio():
    """إنشاء ملف صوتي صامت"""
    
    # إنشاء مجلد الصوت
    audio_dir = Path("assets/backgrounds/audio")
    audio_dir.mkdir(parents=True, exist_ok=True)
    
    # إنشاء ملف صوتي صامت باستخدام ffmpeg
    silent_file = audio_dir / "silent.mp3"
    
    try:
        # إنشاء ملف صوتي صامت لمدة 10 دقائق
        import subprocess
        cmd = [
            "ffmpeg", "-y",  # -y للكتابة فوق الملف الموجود
            "-f", "lavfi",   # استخدام lavfi
            "-i", "anullsrc=channel_layout=stereo:sample_rate=44100",  # صوت صامت
            "-t", "600",     # مدة 10 دقائق
            "-c:a", "mp3",   # ترميز MP3
            str(silent_file)
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ تم إنشاء ملف صوتي صامت: {silent_file}")
            return True
        else:
            print(f"❌ خطأ في ffmpeg: {result.stderr}")
            return False
            
    except FileNotFoundError:
        print("❌ ffmpeg غير مثبت، سأنشئ ملف صامت بطريقة أخرى...")
        
        # طريقة بديلة باستخدام Python
        try:
            import numpy as np
            from scipy.io.wavfile import write
            
            # إنشاء صوت صامت
            sample_rate = 44100
            duration = 600  # 10 دقائق
            samples = int(sample_rate * duration)
            
            # إنشاء array صامت
            silent_audio = np.zeros((samples, 2), dtype=np.float32)
            
            # حفظ كملف WAV أولاً
            wav_file = audio_dir / "silent.wav"
            write(str(wav_file), sample_rate, silent_audio)
            
            print(f"✅ تم إنشاء ملف صوتي صامت: {wav_file}")
            return True
            
        except ImportError:
            print("❌ المكتبات المطلوبة غير مثبتة")
            
            # طريقة أخيرة: إنشاء ملف فارغ
            with open(silent_file, 'wb') as f:
                # كتابة header MP3 بسيط
                f.write(b'\xff\xfb\x90\x00')  # MP3 header
                f.write(b'\x00' * 1000)       # بيانات صامتة
            
            print(f"✅ تم إنشاء ملف صوتي بديل: {silent_file}")
            return True

def update_background_audio_config():
    """تحديث إعدادات الصوت لاستخدام الملف الصامت"""
    
    import json
    from pathlib import Path
    
    # تحديث ملف background_audios.json
    audio_json_path = Path("utils/background_audios.json")
    
    if audio_json_path.exists():
        with open(audio_json_path, 'r', encoding='utf-8') as f:
            audios = json.load(f)
    else:
        audios = {"__comment": "Supported Background Audio. Can add/remove background audio here..."}
    
    # إضافة الصوت الصامت
    audios["silent"] = [
        "local_file",
        "silent.mp3",
        "Local Silent"
    ]
    
    # حفظ التحديث
    with open(audio_json_path, 'w', encoding='utf-8') as f:
        json.dump(audios, f, indent=4, ensure_ascii=False)
    
    print("✅ تم تحديث إعدادات الصوت")

def install_required_packages():
    """تثبيت المكتبات المطلوبة"""
    import subprocess
    import sys
    
    packages = ["numpy", "scipy"]
    
    for package in packages:
        try:
            __import__(package)
            print(f"✅ {package} مثبت بالفعل")
        except ImportError:
            print(f"🔄 تثبيت {package}...")
            try:
                subprocess.run([sys.executable, "-m", "pip", "install", package], 
                             check=True, capture_output=True)
                print(f"✅ تم تثبيت {package}")
            except subprocess.CalledProcessError:
                print(f"❌ فشل في تثبيت {package}")

if __name__ == "__main__":
    print("🔇 إنشاء ملف صوتي صامت")
    print("=" * 30)
    
    # محاولة تثبيت المكتبات المطلوبة
    install_required_packages()
    
    # إنشاء الملف الصامت
    if create_silent_audio():
        update_background_audio_config()
        print("\n🎉 تم! يمكنك الآن تشغيل البوت بدون مشاكل تحميل الصوت")
        print("💡 لتفعيل الصوت لاحقاً، غير background_audio_volume في config.toml")
    else:
        print("\n❌ فشل في إنشاء الملف الصامت")
        print("💡 جرب تعطيل الصوت الخلفي بتعيين background_audio_volume = 0")
